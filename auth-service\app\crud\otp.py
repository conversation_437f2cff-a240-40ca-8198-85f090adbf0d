from sqlalchemy.orm import Session
from datetime import datetime, timedelta, timezone
from typing import Optional
import secrets
import string
import hashlib
import logging

from app.models.otp import OTP
from app.models.user import User
from app.core.config import settings

logger = logging.getLogger(__name__)

def generate_otp_code(length: int = None) -> str:
    """
    Generate a cryptographically secure OTP code.

    Args:
        length: Length of the OTP code (defaults to settings.OTP_LENGTH)

    Returns:
        A secure random OTP code
    """
    if length is None:
        length = settings.OTP_LENGTH

    # Ensure length is within reasonable bounds
    if length < 4 or length > 12:
        raise ValueError("OTP length must be between 4 and 12 digits")

    # Use cryptographically secure random number generation
    # Generate more entropy than needed and hash it for additional security
    entropy = secrets.token_bytes(32)  # 256 bits of entropy
    hash_digest = hashlib.sha256(entropy).hexdigest()

    # Extract digits from the hash and take the required length
    digits = ''.join(char for char in hash_digest if char.isdigit())

    # If we don't have enough digits from the hash, generate more
    while len(digits) < length:
        additional_entropy = secrets.token_bytes(16)
        additional_hash = hashlib.sha256(additional_entropy).hexdigest()
        digits += ''.join(char for char in additional_hash if char.isdigit())

    # Take exactly the required number of digits
    otp_code = digits[:length]

    # Ensure the OTP doesn't start with 0 for better user experience
    if otp_code[0] == '0':
        # Replace first digit with a random non-zero digit
        otp_code = str(secrets.randbelow(9) + 1) + otp_code[1:]

    logger.info(f"Generated OTP code of length {length}")
    return otp_code

def create_otp(db: Session, user_id: int, purpose: str = "password_reset") -> OTP:
    """Create a new OTP for a user with configurable expiration"""
    # Invalidate any existing OTPs for this user and purpose
    db.query(OTP).filter(
        OTP.user_id == user_id,
        OTP.purpose == purpose,
        OTP.is_used == False
    ).update({"is_used": True})

    # Create new OTP with configurable settings
    otp_code = generate_otp_code(settings.OTP_LENGTH)
    expires_at = datetime.now(timezone.utc) + timedelta(minutes=settings.OTP_EXPIRE_MINUTES)

    db_otp = OTP(
        user_id=user_id,
        otp_code=otp_code,
        purpose=purpose,
        expires_at=expires_at
    )

    db.add(db_otp)
    db.commit()
    db.refresh(db_otp)

    logger.info(f"Created OTP for user {user_id}, purpose: {purpose}, expires at: {expires_at}")
    return db_otp

def verify_otp(db: Session, user_id: int, otp_code: str, purpose: str = "password_reset") -> bool:
    """Verify an OTP code for a user"""
    current_time = datetime.now(timezone.utc)
    otp = db.query(OTP).filter(
        OTP.user_id == user_id,
        OTP.otp_code == otp_code,
        OTP.purpose == purpose,
        OTP.is_used == False,
        OTP.expires_at > current_time
    ).first()

    if otp:
        logger.info(f"OTP verified successfully for user {user_id}, purpose: {purpose}")
    else:
        logger.warning(f"OTP verification failed for user {user_id}, purpose: {purpose}")

    return otp is not None

def use_otp(db: Session, user_id: int, otp_code: str, purpose: str = "password_reset") -> bool:
    """Mark an OTP as used after successful verification"""
    current_time = datetime.now(timezone.utc)
    otp = db.query(OTP).filter(
        OTP.user_id == user_id,
        OTP.otp_code == otp_code,
        OTP.purpose == purpose,
        OTP.is_used == False,
        OTP.expires_at > current_time
    ).first()

    if otp:
        otp.is_used = True
        db.commit()
        logger.info(f"OTP marked as used for user {user_id}, purpose: {purpose}")
        return True

    logger.warning(f"Failed to mark OTP as used for user {user_id}, purpose: {purpose}")
    return False

def get_valid_otp(db: Session, user_id: int, otp_code: str, purpose: str = "password_reset") -> Optional[OTP]:
    """Get a valid OTP for a user"""
    current_time = datetime.now(timezone.utc)
    return db.query(OTP).filter(
        OTP.user_id == user_id,
        OTP.otp_code == otp_code,
        OTP.purpose == purpose,
        OTP.is_used == False,
        OTP.expires_at > current_time
    ).first()

def cleanup_expired_otps(db: Session):
    """Clean up expired OTPs"""
    current_time = datetime.now(timezone.utc)
    expired_count = db.query(OTP).filter(OTP.expires_at < current_time).count()
    db.query(OTP).filter(OTP.expires_at < current_time).delete()
    db.commit()

    if expired_count > 0:
        logger.info(f"Cleaned up {expired_count} expired OTPs")

def check_otp_rate_limit(db: Session, user_id: int, purpose: str = "password_reset") -> bool:
    """
    Check if user has exceeded OTP request rate limit.

    Returns:
        True if rate limit is exceeded, False otherwise
    """
    current_time = datetime.now(timezone.utc)
    rate_limit_window = current_time - timedelta(minutes=settings.OTP_RATE_LIMIT_MINUTES)

    # Count OTPs created within the rate limit window
    recent_otps_count = db.query(OTP).filter(
        OTP.user_id == user_id,
        OTP.purpose == purpose,
        OTP.created_at > rate_limit_window
    ).count()

    is_rate_limited = recent_otps_count >= settings.OTP_MAX_REQUESTS_PER_WINDOW

    if is_rate_limited:
        logger.warning(f"Rate limit exceeded for user {user_id}, purpose: {purpose}. "
                      f"Requests: {recent_otps_count}/{settings.OTP_MAX_REQUESTS_PER_WINDOW}")

    return is_rate_limited

def get_otp_attempts_count(db: Session, user_id: int, otp_code: str, purpose: str = "password_reset") -> int:
    """
    Get the number of failed verification attempts for a specific OTP.
    This would require adding an attempts tracking table or field.
    For now, we'll implement a simple version.
    """
    # This is a placeholder - in a production system, you'd want to track
    # failed attempts in a separate table or add an attempts field to the OTP model
    # For now, we'll check if the OTP exists and is still valid
    current_time = datetime.now(timezone.utc)
    otp = db.query(OTP).filter(
        OTP.user_id == user_id,
        OTP.otp_code == otp_code,
        OTP.purpose == purpose,
        OTP.expires_at > current_time
    ).first()

    # Return 0 for now - this would be enhanced with proper attempt tracking
    return 0 if otp else 1

def is_otp_locked(db: Session, user_id: int, purpose: str = "password_reset") -> bool:
    """
    Check if OTP verification is locked due to too many failed attempts.
    """
    # For now, we'll implement a simple check based on recent failed attempts
    # In a production system, you'd want a more sophisticated lockout mechanism
    current_time = datetime.now(timezone.utc)
    lockout_window = current_time - timedelta(minutes=15)  # 15-minute lockout window

    # Count recent OTPs that might have been attempted
    recent_otps = db.query(OTP).filter(
        OTP.user_id == user_id,
        OTP.purpose == purpose,
        OTP.created_at > lockout_window,
        OTP.is_used == False
    ).count()

    # If there are multiple unused OTPs, it might indicate failed attempts
    is_locked = recent_otps >= settings.OTP_MAX_ATTEMPTS

    if is_locked:
        logger.warning(f"OTP verification locked for user {user_id}, purpose: {purpose}")

    return is_locked