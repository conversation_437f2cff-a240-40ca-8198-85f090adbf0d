"""
Notification service abstraction for sending OTPs and other notifications
through multiple channels (email, SMS, push notifications, etc.)
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from enum import Enum
import logging
from datetime import datetime, timezone

from pydantic import BaseModel, EmailStr
from app.core.config import settings

logger = logging.getLogger(__name__)

class NotificationChannel(str, Enum):
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    WEBHOOK = "webhook"

class NotificationPriority(str, Enum):
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

class NotificationRequest(BaseModel):
    recipient: str  # Email, phone number, device ID, etc.
    subject: Optional[str] = None
    message: str
    channel: NotificationChannel
    priority: NotificationPriority = NotificationPriority.NORMAL
    template_data: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None

class NotificationResponse(BaseModel):
    success: bool
    message_id: Optional[str] = None
    error_message: Optional[str] = None
    sent_at: datetime
    channel: NotificationChannel

class NotificationProvider(ABC):
    """Abstract base class for notification providers"""
    
    @abstractmethod
    async def send(self, request: NotificationRequest) -> NotificationResponse:
        """Send a notification through this provider"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if this provider is available and configured"""
        pass
    
    @abstractmethod
    def get_channel(self) -> NotificationChannel:
        """Get the notification channel this provider handles"""
        pass

class EmailNotificationProvider(NotificationProvider):
    """Email notification provider using the existing email service"""
    
    def __init__(self):
        from app.core.email import send_otp_email, send_password_reset_confirmation_email
        self.send_otp_email = send_otp_email
        self.send_password_reset_confirmation_email = send_password_reset_confirmation_email
    
    async def send(self, request: NotificationRequest) -> NotificationResponse:
        """Send email notification"""
        try:
            if not EmailStr._validate(request.recipient):
                raise ValueError("Invalid email address")
            
            # Handle OTP emails
            if request.template_data and "otp_code" in request.template_data:
                user_name = request.template_data.get("user_name", "User")
                success = await self.send_otp_email(
                    email=request.recipient,
                    otp_code=request.template_data["otp_code"],
                    user_name=user_name
                )
            # Handle password reset confirmation
            elif request.template_data and request.template_data.get("type") == "password_reset_confirmation":
                user_name = request.template_data.get("user_name", "User")
                success = await self.send_password_reset_confirmation_email(
                    email=request.recipient,
                    user_name=user_name
                )
            else:
                # Generic email - would need to implement generic email sending
                logger.warning("Generic email sending not implemented yet")
                success = False
            
            return NotificationResponse(
                success=success,
                message_id=f"email_{datetime.now(timezone.utc).timestamp()}",
                sent_at=datetime.now(timezone.utc),
                channel=NotificationChannel.EMAIL
            )
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {str(e)}")
            return NotificationResponse(
                success=False,
                error_message=str(e),
                sent_at=datetime.now(timezone.utc),
                channel=NotificationChannel.EMAIL
            )
    
    def is_available(self) -> bool:
        """Check if email service is configured"""
        return bool(settings.MAIL_USERNAME and settings.MAIL_PASSWORD)
    
    def get_channel(self) -> NotificationChannel:
        return NotificationChannel.EMAIL

class SMSNotificationProvider(NotificationProvider):
    """SMS notification provider (placeholder for future implementation)"""
    
    async def send(self, request: NotificationRequest) -> NotificationResponse:
        """Send SMS notification"""
        # Placeholder implementation
        logger.info(f"SMS notification would be sent to {request.recipient}: {request.message}")
        
        return NotificationResponse(
            success=False,
            error_message="SMS provider not implemented",
            sent_at=datetime.now(timezone.utc),
            channel=NotificationChannel.SMS
        )
    
    def is_available(self) -> bool:
        """Check if SMS service is configured"""
        return False  # Not implemented yet
    
    def get_channel(self) -> NotificationChannel:
        return NotificationChannel.SMS

class NotificationService:
    """Main notification service that manages multiple providers"""
    
    def __init__(self):
        self.providers: Dict[NotificationChannel, NotificationProvider] = {}
        self._register_providers()
    
    def _register_providers(self):
        """Register available notification providers"""
        # Register email provider
        email_provider = EmailNotificationProvider()
        if email_provider.is_available():
            self.providers[NotificationChannel.EMAIL] = email_provider
            logger.info("Email notification provider registered")
        
        # Register SMS provider (placeholder)
        sms_provider = SMSNotificationProvider()
        if sms_provider.is_available():
            self.providers[NotificationChannel.SMS] = sms_provider
            logger.info("SMS notification provider registered")
    
    async def send_notification(self, request: NotificationRequest) -> NotificationResponse:
        """Send notification through the appropriate provider"""
        provider = self.providers.get(request.channel)
        
        if not provider:
            error_msg = f"No provider available for channel: {request.channel}"
            logger.error(error_msg)
            return NotificationResponse(
                success=False,
                error_message=error_msg,
                sent_at=datetime.now(timezone.utc),
                channel=request.channel
            )
        
        return await provider.send(request)
    
    async def send_otp_notification(
        self,
        recipient: str,
        otp_code: str,
        user_name: str = "User",
        channel: NotificationChannel = NotificationChannel.EMAIL
    ) -> NotificationResponse:
        """Convenience method for sending OTP notifications"""
        request = NotificationRequest(
            recipient=recipient,
            subject="Password Reset OTP",
            message=f"Your OTP code is: {otp_code}",
            channel=channel,
            priority=NotificationPriority.HIGH,
            template_data={
                "otp_code": otp_code,
                "user_name": user_name
            }
        )
        
        return await self.send_notification(request)
    
    async def send_password_reset_confirmation(
        self,
        recipient: str,
        user_name: str = "User",
        channel: NotificationChannel = NotificationChannel.EMAIL
    ) -> NotificationResponse:
        """Convenience method for sending password reset confirmation"""
        request = NotificationRequest(
            recipient=recipient,
            subject="Password Reset Successful",
            message="Your password has been reset successfully.",
            channel=channel,
            priority=NotificationPriority.NORMAL,
            template_data={
                "type": "password_reset_confirmation",
                "user_name": user_name
            }
        )
        
        return await self.send_notification(request)
    
    def get_available_channels(self) -> List[NotificationChannel]:
        """Get list of available notification channels"""
        return list(self.providers.keys())
    
    def is_channel_available(self, channel: NotificationChannel) -> bool:
        """Check if a specific channel is available"""
        return channel in self.providers

# Global notification service instance
notification_service = NotificationService()
