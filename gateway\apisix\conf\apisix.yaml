routes:
  - id: auth-api-routes
    name: "Auth Service API Routes"
    uri: /api/v1/auth/*
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
    plugins:
      cors:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600
      proxy-rewrite:
        regex_uri: ["^/api/v1/auth/(.*)", "/api/v1/$1"]

  - id: auth-direct-routes
    name: "Auth Service Direct Routes"
    uri: /auth/*
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
    plugins:
      cors:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600
      proxy-rewrite:
        regex_uri: ["^/auth/(.*)", "/$1"]

  - id: health-check
    name: "Health Check Route"
    uri: /health
    plugins:
      echo:
        body: "healthy"

#END
