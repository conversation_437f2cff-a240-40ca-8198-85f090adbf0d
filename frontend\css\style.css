/* Custom CSS for Zionix Authentication */

:root {
    --primary-color: #4A90E2;
    --primary-hover: #357ABD;
    --background-dark: #2C3E50;
    --background-darker: #1A252F;
    --text-light: #FFFFFF;
    --text-muted: #B0BEC5;
    --input-bg: #34495E;
    --input-border: #4A5568;
    --input-focus: #4A90E2;
    --social-border: #4A5568;
    --link-color: #4A90E2;
    --link-hover: #357ABD;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-darker) 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-light);
    min-height: 100vh;
}

.auth-container {
    background: rgba(52, 73, 94, 0.95);
    border-radius: 16px;
    padding: 40px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-form {
    width: 100%;
}

.auth-title {
    color: var(--text-light);
    font-size: 2rem;
    font-weight: 600;
    text-align: left;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: var(--text-muted);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 2rem;
}

.form-label {
    color: var(--text-light);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.auth-input {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: 8px;
    color: var(--text-light);
    padding: 12px 16px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.auth-input:focus {
    background-color: var(--input-bg);
    border-color: var(--input-focus);
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
    color: var(--text-light);
}

.auth-input::placeholder {
    color: var(--text-muted);
    opacity: 0.8;
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: var(--text-light);
}

.form-check-input {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    border-radius: 4px;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.form-check-label {
    color: var(--text-light);
    font-size: 0.9rem;
}

.forgot-password {
    color: var(--link-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: var(--link-hover);
    text-decoration: underline;
}

.auth-btn {
    background-color: var(--primary-color);
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: none;
}

.auth-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.social-btn {
    border: 1px solid var(--social-border);
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-light);
    transition: all 0.3s ease;
    background-color: transparent;
}

.social-btn:hover {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: var(--text-light);
    color: var(--text-light);
    transform: translateY(-1px);
}

.auth-switch {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 0;
}

.auth-switch a {
    color: var(--link-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-switch a:hover {
    color: var(--link-hover);
    text-decoration: underline;
}

.terms-link {
    color: var(--link-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.terms-link:hover {
    color: var(--link-hover);
    text-decoration: underline;
}

.social-login {
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 576px) {
    .auth-container {
        margin: 20px;
        padding: 30px 25px;
        max-width: none;
    }
    
    .auth-title {
        font-size: 1.75rem;
    }
    
    .auth-subtitle {
        font-size: 0.85rem;
    }
}

@media (max-width: 400px) {
    .auth-container {
        margin: 15px;
        padding: 25px 20px;
    }
    
    .auth-title {
        font-size: 1.5rem;
    }
}

/* Animation for form switching */
.auth-form {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading state */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Error states */
.is-invalid {
    border-color: #dc3545 !important;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Success states */
.is-valid {
    border-color: #28a745 !important;
}

.valid-feedback {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
