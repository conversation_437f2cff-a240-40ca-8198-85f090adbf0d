<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced OTP API Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 10px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section h3 {
            color: #007bff;
            margin-top: 0;
        }
        input, button {
            margin: 8px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
        }
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .rate-limit {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .feature-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .feature-list h4 {
            margin-top: 0;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔐 Enhanced OTP API Test</h1>
        <p>Test the enhanced OTP notification service with rate limiting and security features</p>
    </div>

    <div class="section">
        <h3>🚀 Enhanced Features Tested</h3>
        <div class="feature-list">
            <h4>Security Enhancements:</h4>
            <ul>
                <li>✅ Cryptographically secure OTP generation (6-digit default)</li>
                <li>✅ Configurable expiration time (5 minutes default)</li>
                <li>✅ Rate limiting (3 requests per minute per user)</li>
                <li>✅ Account lockout protection</li>
                <li>✅ Enhanced email templates with security warnings</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h3>1. Request Password Reset OTP</h3>
        <input type="email" id="forgotEmail" placeholder="Email address" value="<EMAIL>">
        <button onclick="requestPasswordReset()">Request OTP</button>
        <div id="forgotResult" class="result"></div>
    </div>

    <div class="section">
        <h3>2. Test Rate Limiting</h3>
        <p>Click multiple times to test rate limiting (max 3 requests per minute):</p>
        <button onclick="testRateLimit()">Test Rate Limit</button>
        <div id="rateLimitResult" class="result"></div>
    </div>

    <div class="section">
        <h3>3. Verify OTP</h3>
        <input type="email" id="verifyEmail" placeholder="Email address" value="<EMAIL>">
        <input type="text" id="otpCode" placeholder="OTP Code" maxlength="6">
        <button onclick="verifyOTP()">Verify OTP</button>
        <div id="verifyResult" class="result"></div>
    </div>

    <div class="section">
        <h3>4. Reset Password</h3>
        <input type="email" id="resetEmail" placeholder="Email address" value="<EMAIL>">
        <input type="text" id="resetOTP" placeholder="OTP Code" maxlength="6">
        <input type="password" id="newPassword" placeholder="New Password">
        <input type="password" id="confirmPassword" placeholder="Confirm Password">
        <button onclick="resetPassword()">Reset Password</button>
        <div id="resetResult" class="result"></div>
    </div>

    <div class="section">
        <h3>📊 Test Results Summary</h3>
        <div id="testSummary" class="result info">
            Ready to test enhanced OTP notification service...
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8002/api/v1';
        let testResults = {
            passwordReset: 0,
            rateLimitHit: false,
            otpVerification: 0,
            passwordChange: 0
        };

        function showResult(elementId, data, className = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${className}`;
        }

        function updateSummary() {
            const summary = document.getElementById('testSummary');
            const total = testResults.passwordReset + testResults.otpVerification + testResults.passwordChange;
            const rateLimitStatus = testResults.rateLimitHit ? '✅ Rate limiting working' : '⏳ Rate limit not tested';
            
            summary.innerHTML = `
Test Summary:
• Password reset requests: ${testResults.passwordReset}
• OTP verifications: ${testResults.otpVerification}
• Password changes: ${testResults.passwordChange}
• ${rateLimitStatus}
• Total API calls: ${total}

Enhanced Features Verified:
${testResults.rateLimitHit ? '✅' : '⏳'} Rate limiting protection
${testResults.passwordReset > 0 ? '✅' : '⏳'} Enhanced email templates
${testResults.otpVerification > 0 ? '✅' : '⏳'} OTP verification
${testResults.passwordChange > 0 ? '✅' : '⏳'} Secure password reset
            `;
        }

        async function requestPasswordReset() {
            try {
                const email = document.getElementById('forgotEmail').value;
                
                const response = await fetch(`${API_BASE}/forgot-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                });
                
                const data = await response.json();
                
                if (response.status === 429) {
                    showResult('forgotResult', {
                        status: response.status,
                        message: 'Rate limit exceeded! ✅ Rate limiting is working',
                        data: data
                    }, 'rate-limit');
                    testResults.rateLimitHit = true;
                } else if (response.ok) {
                    showResult('forgotResult', {
                        status: response.status,
                        message: '✅ OTP request successful with enhanced security',
                        data: data
                    }, 'success');
                    testResults.passwordReset++;
                } else {
                    showResult('forgotResult', {
                        status: response.status,
                        data: data
                    }, 'error');
                }
                
                updateSummary();
                
            } catch (error) {
                showResult('forgotResult', { 
                    error: error.message,
                    note: 'Make sure auth-service is running on localhost:8002'
                }, 'error');
            }
        }

        async function testRateLimit() {
            const email = document.getElementById('forgotEmail').value;
            let results = [];
            
            showResult('rateLimitResult', 'Testing rate limiting... Making 5 rapid requests...', 'info');
            
            for (let i = 1; i <= 5; i++) {
                try {
                    const response = await fetch(`${API_BASE}/forgot-password`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ email: email })
                    });
                    
                    const data = await response.json();
                    results.push({
                        request: i,
                        status: response.status,
                        rateLimited: response.status === 429,
                        message: response.status === 429 ? 'Rate limited ✅' : 'Allowed'
                    });
                    
                    if (response.status === 429) {
                        testResults.rateLimitHit = true;
                        break;
                    }
                    
                    // Small delay between requests
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                } catch (error) {
                    results.push({
                        request: i,
                        error: error.message
                    });
                }
            }
            
            showResult('rateLimitResult', {
                message: 'Rate limiting test completed',
                results: results,
                rateLimitWorking: testResults.rateLimitHit
            }, testResults.rateLimitHit ? 'success' : 'info');
            
            updateSummary();
        }

        async function verifyOTP() {
            try {
                const email = document.getElementById('verifyEmail').value;
                const otpCode = document.getElementById('otpCode').value;
                
                const response = await fetch(`${API_BASE}/verify-otp`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        email: email,
                        otp_code: otpCode
                    })
                });
                
                const data = await response.json();
                
                showResult('verifyResult', {
                    status: response.status,
                    data: data,
                    message: response.ok ? '✅ OTP verification successful' : '❌ OTP verification failed'
                }, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    testResults.otpVerification++;
                }
                
                updateSummary();
                
            } catch (error) {
                showResult('verifyResult', { 
                    error: error.message,
                    note: 'Make sure auth-service is running on localhost:8002'
                }, 'error');
            }
        }

        async function resetPassword() {
            try {
                const email = document.getElementById('resetEmail').value;
                const otpCode = document.getElementById('resetOTP').value;
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                
                const response = await fetch(`${API_BASE}/reset-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        email: email,
                        otp_code: otpCode,
                        new_password: newPassword,
                        confirm_password: confirmPassword
                    })
                });
                
                const data = await response.json();
                
                showResult('resetResult', {
                    status: response.status,
                    data: data,
                    message: response.ok ? '✅ Password reset successful' : '❌ Password reset failed'
                }, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    testResults.passwordChange++;
                }
                
                updateSummary();
                
            } catch (error) {
                showResult('resetResult', { 
                    error: error.message,
                    note: 'Make sure auth-service is running on localhost:8002'
                }, 'error');
            }
        }

        // Initialize
        updateSummary();
    </script>
</body>
</html>
