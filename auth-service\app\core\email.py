import os
from typing import List
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig, MessageType
from pydantic import EmailStr
import logging

logger = logging.getLogger(__name__)

# Email configuration
conf = ConnectionConfig(
    MAIL_USERNAME=os.getenv("MAIL_USERNAME", "Zionix"),
    MAIL_PASSWORD=os.getenv("MAIL_PASSWORD", "Arun@1234"),
    MAIL_FROM=os.getenv("MAIL_FROM", "<EMAIL>"),
    MAIL_PORT=int(os.getenv("MAIL_PORT", "587")),
    MAIL_SERVER=os.getenv("MAIL_SERVER", "smtp.gmail.com"),
    MAIL_FROM_NAME=os.getenv("MAIL_FROM_NAME", "Zionix Auth Service"),
    MAIL_STARTTLS=True,
    MAIL_SSL_TLS=False,
    USE_CREDENTIALS=True,
    VALIDATE_CERTS=True
)

fastmail = FastMail(conf)

async def send_otp_email(email: EmailStr, otp_code: str, user_name: str = "User") -> bool:
    """Send enhanced OTP email for password reset"""
    try:
        from app.core.email_templates import get_otp_email_template

        html_content, text_content = get_otp_email_template(otp_code, user_name)

        message = MessageSchema(
            subject="🔐 Password Reset OTP - Zionix",
            recipients=[email],
            body=text_content,
            html=html_content,
            subtype=MessageType.html
        )

        await fastmail.send_message(message)
        logger.info(f"Enhanced OTP email sent successfully to {email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send OTP email to {email}: {str(e)}")
        return False

async def send_password_reset_confirmation_email(email: EmailStr, user_name: str = "User") -> bool:
    """Send enhanced confirmation email after successful password reset"""
    try:
        from app.core.email_templates import get_password_reset_confirmation_template

        html_content, text_content = get_password_reset_confirmation_template(user_name)

        message = MessageSchema(
            subject="✅ Password Reset Successful - Zionix",
            recipients=[email],
            body=text_content,
            html=html_content,
            subtype=MessageType.html
        )

        await fastmail.send_message(message)
        logger.info(f"Enhanced password reset confirmation email sent successfully to {email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send password reset confirmation email to {email}: {str(e)}")
        return False