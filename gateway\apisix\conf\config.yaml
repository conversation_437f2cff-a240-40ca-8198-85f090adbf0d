apisix:
  node_listen: 9080
  enable_ipv6: false

deployment:
  role: data_plane
  role_data_plane:
    config_provider: yaml

plugin_attr:
  prometheus:
    export_addr:
      ip: "0.0.0.0"
      port: 9091

plugins:
  - real-ip
  - client-control
  - proxy-control
  - request-id
  - zipkin
  - ext-plugin-pre-req
  - fault-injection
  - mocking
  - serverless-pre-function
  - cors
  - ip-restriction
  - ua-restriction
  - referer-restriction
  - csrf
  - uri-blocker
  - request-validation
  - openid-connect
  - authz-casbin
  - authz-casdoor
  - wolf-rbac
  - ldap-auth
  - hmac-auth
  - basic-auth
  - jwt-auth
  - key-auth
  - consumer-restriction
  - authz-keycloak
  - opa
  - forward-auth
  - multi-auth
  - api-breaker
  - limit-req
  - limit-count
  - limit-conn
  - degraphql
  - body-transformer
  - workflow
  - proxy-cache
  - proxy-mirror
  - proxy-rewrite
  - workflow
  - api-breaker
  - limit-req
  - limit-count
  - limit-conn
  - gzip
  - server-info
  - traffic-split
  - redirect
  - response-rewrite
  - kafka-logger
  - rocketmq-logger
  - tcp-logger
  - kafka-proxy
  - dubbo-proxy
  - grpc-transcode
  - grpc-web
  - public-api
  - prometheus
  - datadog
  - echo
  - http-logger
  - splunk-hec-logging
  - skywalking-logger
  - google-cloud-logging
  - sls-logger
  - tcp-logger
  - kafka-logger
  - rocketmq-logger
  - udp-logger
  - clickhouse-logger
  - tencent-cloud-cls
  - inspect
  - example-plugin
  - aws-lambda
  - azure-functions
  - openwhisk
  - serverless-post-function
  - ext-plugin-post-req
  - ext-plugin-post-resp
