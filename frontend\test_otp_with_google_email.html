<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test OTP with Your Google Email</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 8px;
        }
        .step {
            margin: 25px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .step.active {
            border-color: #007bff;
            background: #e7f3ff;
        }
        .step.completed {
            border-color: #28a745;
            background: #d4edda;
        }
        .step h3 {
            margin-top: 0;
            color: #007bff;
        }
        .step.completed h3 {
            color: #28a745;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            margin: 8px 0;
            box-sizing: border-box;
        }
        button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            font-weight: 600;
            width: 100%;
            margin: 10px 0;
        }
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .message {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-weight: 500;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .hidden {
            display: none;
        }
        .otp-input {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            letter-spacing: 8px;
            font-family: monospace;
        }
        .security-note {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
        .feature-highlight {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Test Enhanced OTP Service</h1>
            <p>Test password reset with your Google email</p>
        </div>

        <div class="feature-highlight">
            <h4>🚀 Enhanced Features Active:</h4>
            <ul>
                <li>✅ Cryptographically secure 6-digit OTP</li>
                <li>✅ Professional email templates with security warnings</li>
                <li>✅ Rate limiting (max 3 requests per minute)</li>
                <li>✅ 5-minute expiration for security</li>
                <li>✅ Account lockout protection</li>
            </ul>
        </div>

        <!-- Step 1: Request OTP -->
        <div class="step active" id="step1">
            <h3>Step 1: Request OTP</h3>
            <p>Enter your Google email address to receive a secure OTP:</p>
            <input type="email" id="emailInput" placeholder="<EMAIL>" required>
            <button onclick="requestOTP()">Send OTP to My Email</button>
            <div class="security-note">
                <strong>🛡️ Security Note:</strong> The OTP will be sent to your email with enhanced security warnings and professional formatting.
            </div>
        </div>

        <!-- Step 2: Verify OTP -->
        <div class="step hidden" id="step2">
            <h3>Step 2: Verify OTP</h3>
            <p>Enter the 6-digit code you received in your email:</p>
            <input type="text" id="otpInput" placeholder="123456" maxlength="6" class="otp-input" required>
            <button onclick="verifyOTP()">Verify OTP Code</button>
            <button onclick="requestOTP()" style="background: #6c757d;">Resend OTP</button>
        </div>

        <!-- Step 3: Reset Password -->
        <div class="step hidden" id="step3">
            <h3>Step 3: Reset Password</h3>
            <p>Enter your new password:</p>
            <input type="password" id="newPasswordInput" placeholder="New Password (min 8 characters)" required>
            <input type="password" id="confirmPasswordInput" placeholder="Confirm New Password" required>
            <button onclick="resetPassword()">Reset Password</button>
        </div>

        <!-- Step 4: Success -->
        <div class="step hidden" id="step4">
            <h3>✅ Password Reset Complete!</h3>
            <p>Your password has been successfully reset. You can now log in with your new password.</p>
            <button onclick="startOver()">Test Again</button>
        </div>

        <!-- Messages -->
        <div id="messageArea"></div>

        <!-- Debug Info -->
        <div class="step" style="margin-top: 30px;">
            <h3>📊 Test Status</h3>
            <div id="debugInfo">
                <p><strong>Auth Service:</strong> <span id="serviceStatus">Checking...</span></p>
                <p><strong>Current Step:</strong> <span id="currentStep">1 - Request OTP</span></p>
                <p><strong>Email:</strong> <span id="currentEmail">Not set</span></p>
                <p><strong>OTP Status:</strong> <span id="otpStatus">Not requested</span></p>
            </div>
        </div>
    </div>

    <!-- Include your enhanced auth.js -->
    <script src="js/auth.js"></script>
    
    <script>
        let currentEmail = '';
        let currentOTP = '';

        // Check auth service status on load
        window.onload = function() {
            checkServiceStatus();
        };

        async function checkServiceStatus() {
            try {
                const response = await fetch('http://localhost:8002/health');
                if (response.ok) {
                    document.getElementById('serviceStatus').innerHTML = '<span style="color: green;">✅ Running</span>';
                } else {
                    document.getElementById('serviceStatus').innerHTML = '<span style="color: red;">❌ Error</span>';
                }
            } catch (error) {
                document.getElementById('serviceStatus').innerHTML = '<span style="color: red;">❌ Not running</span>';
                showMessage('⚠️ Auth service is not running. Please start it with: docker-compose up auth-service', 'error');
            }
        }

        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('messageArea');
            messageArea.innerHTML = `<div class="message ${type}">${message}</div>`;
            
            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    messageArea.innerHTML = '';
                }, 5000);
            }
        }

        function updateStep(stepNumber) {
            // Hide all steps
            for (let i = 1; i <= 4; i++) {
                const step = document.getElementById(`step${i}`);
                step.classList.remove('active');
                step.classList.add('hidden');
                if (i < stepNumber) {
                    step.classList.add('completed');
                    step.classList.remove('hidden');
                }
            }
            
            // Show current step
            const currentStepElement = document.getElementById(`step${stepNumber}`);
            currentStepElement.classList.remove('hidden');
            currentStepElement.classList.add('active');
            
            // Update debug info
            const stepNames = ['', 'Request OTP', 'Verify OTP', 'Reset Password', 'Complete'];
            document.getElementById('currentStep').textContent = `${stepNumber} - ${stepNames[stepNumber]}`;
        }

        async function requestOTP() {
            const email = document.getElementById('emailInput').value;
            
            if (!email) {
                showMessage('Please enter your email address.', 'error');
                return;
            }
            
            if (!email.includes('@')) {
                showMessage('Please enter a valid email address.', 'error');
                return;
            }
            
            currentEmail = email;
            document.getElementById('currentEmail').textContent = email;
            
            showMessage('🔄 Sending OTP to your email...', 'loading');
            
            const result = await requestPasswordResetOTP(email);
            
            if (result.success) {
                document.getElementById('otpStatus').textContent = 'Sent successfully';
                updateStep(2);
                showMessage('📧 OTP sent! Check your Gmail inbox (and spam folder). The code expires in 5 minutes.', 'success');
            } else {
                if (result.error === 'rate_limited') {
                    showMessage('⏰ Rate limit exceeded! Please wait before requesting another OTP.', 'error');
                } else if (result.error === 'locked') {
                    showMessage('🔒 Account temporarily locked. Please try again later.', 'error');
                } else {
                    showMessage('❌ Failed to send OTP. Please check your email and try again.', 'error');
                }
            }
        }

        async function verifyOTP() {
            const otpCode = document.getElementById('otpInput').value;
            
            if (!otpCode || otpCode.length !== 6) {
                showMessage('Please enter the 6-digit OTP code.', 'error');
                return;
            }
            
            currentOTP = otpCode;
            showMessage('🔄 Verifying OTP code...', 'loading');
            
            const result = await verifyOTPCode(currentEmail, otpCode);
            
            if (result.success) {
                document.getElementById('otpStatus').textContent = 'Verified successfully';
                updateStep(3);
                showMessage('✅ OTP verified! Now you can set your new password.', 'success');
            } else {
                showMessage('❌ Invalid or expired OTP code. Please check your email and try again.', 'error');
            }
        }

        async function resetPassword() {
            const newPassword = document.getElementById('newPasswordInput').value;
            const confirmPassword = document.getElementById('confirmPasswordInput').value;
            
            if (!newPassword || !confirmPassword) {
                showMessage('Please fill in both password fields.', 'error');
                return;
            }
            
            if (newPassword !== confirmPassword) {
                showMessage('Passwords do not match. Please try again.', 'error');
                return;
            }
            
            if (newPassword.length < 8) {
                showMessage('Password must be at least 8 characters long.', 'error');
                return;
            }
            
            showMessage('🔄 Resetting your password...', 'loading');
            
            const result = await resetPasswordWithOTP(currentEmail, currentOTP, newPassword, confirmPassword);
            
            if (result.success) {
                updateStep(4);
                showMessage('🎉 Password reset successfully! You can now log in with your new password.', 'success');
            } else {
                showMessage('❌ Failed to reset password. Please try again.', 'error');
            }
        }

        function startOver() {
            currentEmail = '';
            currentOTP = '';
            document.getElementById('emailInput').value = '';
            document.getElementById('otpInput').value = '';
            document.getElementById('newPasswordInput').value = '';
            document.getElementById('confirmPasswordInput').value = '';
            document.getElementById('currentEmail').textContent = 'Not set';
            document.getElementById('otpStatus').textContent = 'Not requested';
            updateStep(1);
            showMessage('Ready to test again!', 'info');
        }

        // Auto-format OTP input
        document.getElementById('otpInput').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
            if (value.length > 6) value = value.slice(0, 6); // Limit to 6 digits
            e.target.value = value;
        });
    </script>
</body>
</html>
