"""
OTP Analytics and Monitoring Service
Provides comprehensive logging and analytics for OTP usage patterns.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import func, and_

from app.models.otp import OTP
from app.models.user import User
from app.core.config import settings

logger = logging.getLogger(__name__)

class OTPAnalytics:
    """Service for tracking and analyzing OTP usage patterns"""
    
    @staticmethod
    def log_otp_created(user_id: int, purpose: str, otp_code: str, expires_at: datetime):
        """Log OTP creation event"""
        logger.info(
            f"OTP_CREATED: user_id={user_id}, purpose={purpose}, "
            f"code_length={len(otp_code)}, expires_at={expires_at.isoformat()}"
        )
    
    @staticmethod
    def log_otp_verification_attempt(user_id: int, purpose: str, success: bool, 
                                   ip_address: Optional[str] = None, 
                                   user_agent: Optional[str] = None):
        """Log OTP verification attempt"""
        status = "SUCCESS" if success else "FAILED"
        logger.info(
            f"OTP_VERIFICATION: user_id={user_id}, purpose={purpose}, "
            f"status={status}, ip={ip_address}, user_agent={user_agent}"
        )
    
    @staticmethod
    def log_otp_rate_limit_hit(user_id: int, purpose: str, attempts_count: int):
        """Log when rate limit is hit"""
        logger.warning(
            f"OTP_RATE_LIMIT: user_id={user_id}, purpose={purpose}, "
            f"attempts={attempts_count}, limit={settings.OTP_MAX_REQUESTS_PER_WINDOW}"
        )
    
    @staticmethod
    def log_otp_lockout(user_id: int, purpose: str, reason: str):
        """Log when user is locked out"""
        logger.warning(
            f"OTP_LOCKOUT: user_id={user_id}, purpose={purpose}, reason={reason}"
        )
    
    @staticmethod
    def log_otp_expired(user_id: int, purpose: str, otp_code: str):
        """Log when OTP expires"""
        logger.info(
            f"OTP_EXPIRED: user_id={user_id}, purpose={purpose}, code={otp_code[:2]}***"
        )
    
    @staticmethod
    def get_otp_statistics(db: Session, days: int = 7) -> Dict[str, Any]:
        """Get OTP usage statistics for the last N days"""
        try:
            start_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            # Total OTPs created
            total_otps = db.query(OTP).filter(OTP.created_at >= start_date).count()
            
            # OTPs by purpose
            otps_by_purpose = db.query(
                OTP.purpose, 
                func.count(OTP.id).label('count')
            ).filter(
                OTP.created_at >= start_date
            ).group_by(OTP.purpose).all()
            
            # Success rate (used OTPs vs total OTPs)
            used_otps = db.query(OTP).filter(
                and_(OTP.created_at >= start_date, OTP.is_used == True)
            ).count()
            
            success_rate = (used_otps / total_otps * 100) if total_otps > 0 else 0
            
            # Expired OTPs
            expired_otps = db.query(OTP).filter(
                and_(
                    OTP.created_at >= start_date,
                    OTP.expires_at < datetime.now(timezone.utc),
                    OTP.is_used == False
                )
            ).count()
            
            # Daily breakdown
            daily_stats = db.query(
                func.date(OTP.created_at).label('date'),
                func.count(OTP.id).label('total'),
                func.sum(func.cast(OTP.is_used, db.Integer)).label('used')
            ).filter(
                OTP.created_at >= start_date
            ).group_by(func.date(OTP.created_at)).all()
            
            return {
                "period_days": days,
                "start_date": start_date.isoformat(),
                "end_date": datetime.now(timezone.utc).isoformat(),
                "total_otps_created": total_otps,
                "total_otps_used": used_otps,
                "total_otps_expired": expired_otps,
                "success_rate_percent": round(success_rate, 2),
                "otps_by_purpose": {purpose: count for purpose, count in otps_by_purpose},
                "daily_breakdown": [
                    {
                        "date": str(stat.date),
                        "total_created": stat.total,
                        "total_used": stat.used or 0,
                        "success_rate": round((stat.used or 0) / stat.total * 100, 2) if stat.total > 0 else 0
                    }
                    for stat in daily_stats
                ]
            }
            
        except Exception as e:
            logger.error(f"Error generating OTP statistics: {str(e)}")
            return {"error": str(e)}
    
    @staticmethod
    def get_user_otp_history(db: Session, user_id: int, days: int = 30) -> Dict[str, Any]:
        """Get OTP history for a specific user"""
        try:
            start_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            user_otps = db.query(OTP).filter(
                and_(OTP.user_id == user_id, OTP.created_at >= start_date)
            ).order_by(OTP.created_at.desc()).all()
            
            total_requests = len(user_otps)
            successful_verifications = sum(1 for otp in user_otps if otp.is_used)
            expired_otps = sum(
                1 for otp in user_otps 
                if otp.expires_at < datetime.now(timezone.utc) and not otp.is_used
            )
            
            # Group by purpose
            by_purpose = {}
            for otp in user_otps:
                if otp.purpose not in by_purpose:
                    by_purpose[otp.purpose] = {"total": 0, "used": 0, "expired": 0}
                by_purpose[otp.purpose]["total"] += 1
                if otp.is_used:
                    by_purpose[otp.purpose]["used"] += 1
                elif otp.expires_at < datetime.now(timezone.utc):
                    by_purpose[otp.purpose]["expired"] += 1
            
            return {
                "user_id": user_id,
                "period_days": days,
                "total_otp_requests": total_requests,
                "successful_verifications": successful_verifications,
                "expired_otps": expired_otps,
                "success_rate_percent": round(
                    (successful_verifications / total_requests * 100) if total_requests > 0 else 0, 2
                ),
                "by_purpose": by_purpose,
                "recent_otps": [
                    {
                        "id": otp.id,
                        "purpose": otp.purpose,
                        "created_at": otp.created_at.isoformat(),
                        "expires_at": otp.expires_at.isoformat(),
                        "is_used": otp.is_used,
                        "is_expired": otp.expires_at < datetime.now(timezone.utc)
                    }
                    for otp in user_otps[:10]  # Last 10 OTPs
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting user OTP history for user {user_id}: {str(e)}")
            return {"error": str(e)}
    
    @staticmethod
    def get_security_alerts(db: Session, hours: int = 24) -> List[Dict[str, Any]]:
        """Get security alerts based on OTP usage patterns"""
        try:
            alerts = []
            start_time = datetime.now(timezone.utc) - timedelta(hours=hours)
            
            # Alert 1: Users with excessive OTP requests
            excessive_requests = db.query(
                OTP.user_id,
                func.count(OTP.id).label('request_count')
            ).filter(
                OTP.created_at >= start_time
            ).group_by(OTP.user_id).having(
                func.count(OTP.id) > settings.OTP_MAX_REQUESTS_PER_WINDOW * 3
            ).all()
            
            for user_id, count in excessive_requests:
                alerts.append({
                    "type": "EXCESSIVE_OTP_REQUESTS",
                    "severity": "HIGH",
                    "user_id": user_id,
                    "details": f"User requested {count} OTPs in {hours} hours",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })
            
            # Alert 2: High failure rate for specific users
            user_stats = db.query(
                OTP.user_id,
                func.count(OTP.id).label('total'),
                func.sum(func.cast(OTP.is_used, db.Integer)).label('used')
            ).filter(
                OTP.created_at >= start_time
            ).group_by(OTP.user_id).having(
                func.count(OTP.id) >= 3  # At least 3 attempts
            ).all()
            
            for user_id, total, used in user_stats:
                used = used or 0
                failure_rate = (total - used) / total * 100
                if failure_rate > 80:  # More than 80% failure rate
                    alerts.append({
                        "type": "HIGH_FAILURE_RATE",
                        "severity": "MEDIUM",
                        "user_id": user_id,
                        "details": f"User has {failure_rate:.1f}% OTP failure rate ({used}/{total} successful)",
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    })
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error generating security alerts: {str(e)}")
            return [{"error": str(e)}]

# Global analytics instance
otp_analytics = OTPAnalytics()
