# PowerShell script to generate RSA keys for ZCare microservices
# This script generates RSA key pair for JWT signing

Write-Host "🔑 Generating RSA Keys for ZCare Microservices" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green
Write-Host ""

# Create keys directory if it doesn't exist
if (!(Test-Path "keys")) {
    New-Item -ItemType Directory -Path "keys" | Out-Null
    Write-Host "✅ Created keys directory" -ForegroundColor Green
}

# Check if keys already exist
if ((Test-Path "keys\private.pem") -and (Test-Path "keys\public.pem")) {
    Write-Host "ℹ️  RSA keys already exist" -ForegroundColor Yellow
    $overwrite = Read-Host "Do you want to overwrite them? (y/N)"
    if ($overwrite -ne "y" -and $overwrite -ne "Y") {
        Write-Host "✅ Using existing keys" -ForegroundColor Green
        Write-Host ""
        Write-Host "📋 Environment Variables for Render:" -ForegroundColor Blue
        Write-Host "====================================" -ForegroundColor Blue
        Write-Host ""
        Write-Host "RSA_PRIVATE_KEY:"
        $privateKey = Get-Content "keys\private.pem" -Raw
        $privateKeyBase64 = [Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($privateKey))
        Write-Host $privateKeyBase64
        Write-Host ""
        Write-Host "RSA_PUBLIC_KEY:"
        $publicKey = Get-Content "keys\public.pem" -Raw
        $publicKeyBase64 = [Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($publicKey))
        Write-Host $publicKeyBase64
        Write-Host ""
        Write-Host "⚠️  Save these values securely for Render deployment" -ForegroundColor Yellow
        return
    }
}

try {
    # Generate RSA key pair using .NET cryptography
    Write-Host "ℹ️  Generating RSA key pair..." -ForegroundColor Blue

    # Create RSA provider
    $rsa = [System.Security.Cryptography.RSA]::Create(2048)

    # Export private key in PKCS#8 format (more compatible)
    $privateKeyBytes = $rsa.ExportPkcs8PrivateKey()
    $privateKeyPem = "-----BEGIN PRIVATE KEY-----`n"
    $privateKeyPem += [Convert]::ToBase64String($privateKeyBytes, [System.Base64FormattingOptions]::InsertLineBreaks)
    $privateKeyPem += "`n-----END PRIVATE KEY-----"

    # Export public key in X.509 format
    $publicKeyBytes = $rsa.ExportSubjectPublicKeyInfo()
    $publicKeyPem = "-----BEGIN PUBLIC KEY-----`n"
    $publicKeyPem += [Convert]::ToBase64String($publicKeyBytes, [System.Base64FormattingOptions]::InsertLineBreaks)
    $publicKeyPem += "`n-----END PUBLIC KEY-----"

    # Save keys to files
    $privateKeyPem | Out-File -FilePath "keys\private.pem" -Encoding UTF8 -NoNewline
    $publicKeyPem | Out-File -FilePath "keys\public.pem" -Encoding UTF8 -NoNewline
    
    Write-Host "✅ RSA keys generated successfully" -ForegroundColor Green
    Write-Host ""
    
    # Display environment variables
    Write-Host "📋 Environment Variables for Render:" -ForegroundColor Blue
    Write-Host "====================================" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Copy these values and set them in your Render service environment variables:"
    Write-Host ""
    
    Write-Host "RSA_PRIVATE_KEY:"
    $privateKeyBase64 = [Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($privateKeyPem))
    Write-Host $privateKeyBase64
    Write-Host ""
    
    Write-Host "RSA_PUBLIC_KEY:"
    $publicKeyBase64 = [Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($publicKeyPem))
    Write-Host $publicKeyBase64
    Write-Host ""
    
    Write-Host "⚠️  Save these values securely - you'll need them for both auth-service and admin-service" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "✅ Keys saved to:" -ForegroundColor Green
    Write-Host "   - keys\private.pem"
    Write-Host "   - keys\public.pem"
    
} catch {
    Write-Host "❌ Failed to generate RSA keys: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Alternative: Use OpenSSL commands:" -ForegroundColor Yellow
    Write-Host "  openssl genrsa -out keys\private.pem 2048"
    Write-Host "  openssl rsa -in keys\private.pem -pubout -out keys\public.pem"
    exit 1
}

Write-Host ""
Write-Host "🎉 RSA key generation completed!" -ForegroundColor Green
