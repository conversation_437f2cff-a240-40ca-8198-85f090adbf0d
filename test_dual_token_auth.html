<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dual Token Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
        }
        input, button {
            margin: 5px;
            padding: 8px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Dual Token Authentication System Test</h1>
    
    <div class="section">
        <h3>1. Register User</h3>
        <input type="email" id="regEmail" placeholder="Email" value="<EMAIL>">
        <input type="text" id="regFirstName" placeholder="First Name" value="Test">
        <input type="text" id="regLastName" placeholder="Last Name" value="User">
        <input type="password" id="regPassword" placeholder="Password" value="testpassword123">
        <button onclick="registerUser()">Register</button>
        <div id="registerResult" class="result"></div>
    </div>

    <div class="section">
        <h3>2. Login (Get Both Tokens)</h3>
        <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
        <input type="password" id="loginPassword" placeholder="Password" value="testpassword123">
        <button onclick="loginUser()">Login</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="section">
        <h3>3. Access Protected Route</h3>
        <button onclick="accessProtectedRoute()">Access /protected</button>
        <div id="protectedResult" class="result"></div>
    </div>

    <div class="section">
        <h3>4. Refresh Access Token</h3>
        <button onclick="refreshToken()">Refresh Token</button>
        <div id="refreshResult" class="result"></div>
    </div>

    <div class="section">
        <h3>5. Get Current User Info</h3>
        <button onclick="getCurrentUser()">Get /me</button>
        <div id="userResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8002/api/v1';
        let accessToken = '';
        let refreshToken = '';

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        async function registerUser() {
            try {
                const response = await fetch(`${API_BASE}/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        email: document.getElementById('regEmail').value,
                        password: document.getElementById('regPassword').value,
                        confirm_password: document.getElementById('regPassword').value,
                        first_name: document.getElementById('regFirstName').value,
                        last_name: document.getElementById('regLastName').value
                    })
                });
                
                const data = await response.json();
                showResult('registerResult', {
                    status: response.status,
                    data: data
                }, !response.ok);
            } catch (error) {
                showResult('registerResult', { error: error.message }, true);
            }
        }

        async function loginUser() {
            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        username: document.getElementById('loginEmail').value,
                        password: document.getElementById('loginPassword').value
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    accessToken = data.access_token;
                    refreshToken = data.refresh_token;
                }
                
                showResult('loginResult', {
                    status: response.status,
                    data: data,
                    tokens_stored: response.ok
                }, !response.ok);
            } catch (error) {
                showResult('loginResult', { error: error.message }, true);
            }
        }

        async function accessProtectedRoute() {
            try {
                const response = await fetch(`${API_BASE}/protected`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`
                    }
                });
                
                const data = await response.json();
                showResult('protectedResult', {
                    status: response.status,
                    data: data
                }, !response.ok);
            } catch (error) {
                showResult('protectedResult', { error: error.message }, true);
            }
        }

        async function refreshToken() {
            try {
                const response = await fetch(`${API_BASE}/refresh`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        refresh_token: refreshToken
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    accessToken = data.access_token;
                }
                
                showResult('refreshResult', {
                    status: response.status,
                    data: data,
                    new_token_stored: response.ok
                }, !response.ok);
            } catch (error) {
                showResult('refreshResult', { error: error.message }, true);
            }
        }

        async function getCurrentUser() {
            try {
                const response = await fetch(`${API_BASE}/me`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`
                    }
                });
                
                const data = await response.json();
                showResult('userResult', {
                    status: response.status,
                    data: data
                }, !response.ok);
            } catch (error) {
                showResult('userResult', { error: error.message }, true);
            }
        }
    </script>
</body>
</html>
