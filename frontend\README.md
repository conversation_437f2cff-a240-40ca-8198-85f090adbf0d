# Zionix Frontend - Authentication UI

This folder contains a responsive frontend implementation for the Zionix authentication system with login and register functionality.

## Features

- **Responsive Design**: Built with Bootstrap 5 for mobile-first responsive design
- **Dark Theme UI**: Matches the provided design with dark gradient background
- **Login & Register Forms**: Complete authentication forms with validation
- **Password Visibility Toggle**: Eye icon to show/hide passwords
- **Social Login Buttons**: Google and Facebook login options (UI only)
- **Form Validation**: Client-side validation with error messages
- **API Integration**: Ready to connect with auth-service backend
- **Loading States**: Visual feedback during form submission
- **Remember Me**: Checkbox for persistent login (UI ready)

## File Structure

```
frontend/
├── index.html          # Main HTML file with login and register forms
├── css/
│   └── style.css       # Custom CSS with dark theme styling
├── js/
│   └── auth.js         # JavaScript for form handling and API calls
└── README.md           # This documentation file
```

## Design Features

### Login Form
- Email and password fields
- Remember me checkbox
- Forgot password link
- Social login buttons (Google, Facebook)
- Switch to register form

### Register Form
- First name and last name fields
- Email and password fields
- Confirm password field
- Terms of use agreement checkbox
- Social signup buttons (Google, Facebook)
- Switch to login form

### UI Elements
- **Color Scheme**: Dark blue gradient background (#2C3E50 to #1A252F)
- **Primary Color**: Blue (#4A90E2)
- **Typography**: Segoe UI font family
- **Form Styling**: Dark input fields with blue focus states
- **Buttons**: Primary blue buttons with hover effects
- **Responsive**: Works on desktop, tablet, and mobile devices

## Setup Instructions

1. **Open the Frontend**:
   ```bash
   # Navigate to the frontend folder
   cd frontend
   
   # Open index.html in your browser
   # You can use a simple HTTP server for better development experience
   ```

2. **Using a Local Server** (Recommended):
   ```bash
   # Using Python (if installed)
   python -m http.server 8080
   
   # Using Node.js (if installed)
   npx http-server -p 8080
   
   # Then open http://localhost:8080 in your browser
   ```

3. **Configure API Endpoints**:
   - Edit `js/auth.js`
   - Update the `API_CONFIG.AUTH_SERVICE_URL` to match your auth-service URL
   - Default is set to `http://localhost:8001`

## API Integration

The frontend is configured to work with the auth-service backend:

### Login Endpoint
- **URL**: `POST /auth/login`
- **Content-Type**: `application/x-www-form-urlencoded`
- **Body**: `username` (email) and `password`
- **Response**: Access token and token type

### Register Endpoint
- **URL**: `POST /auth/register`
- **Content-Type**: `application/json`
- **Body**: `email`, `first_name`, `last_name`, `password`
- **Response**: User creation confirmation

## Customization

### Styling
- Modify `css/style.css` to change colors, fonts, or layout
- CSS variables are defined in `:root` for easy theme customization
- Bootstrap classes can be overridden for specific styling needs

### Functionality
- Edit `js/auth.js` to modify form validation rules
- Add additional form fields as needed
- Implement social login functionality
- Add toast notifications or modal dialogs

### Responsive Breakpoints
- Mobile: < 576px
- Tablet: 576px - 768px
- Desktop: > 768px

## Browser Compatibility

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Dependencies

### External CDN Resources
- **Bootstrap 5.3.0**: CSS framework for responsive design
- **Font Awesome 6.0.0**: Icons for UI elements
- **No jQuery required**: Pure vanilla JavaScript implementation

## Security Considerations

- Form validation is implemented on both client and server side
- Passwords are never stored in localStorage
- Access tokens are stored securely in localStorage
- HTTPS should be used in production
- CORS should be properly configured on the backend

## Development Notes

- The frontend uses modern JavaScript (ES6+)
- No build process required - can be served as static files
- Form switching is handled with JavaScript show/hide
- Password strength validation can be enhanced as needed
- Social login buttons are UI-only and need backend implementation

## Production Deployment

1. **Static File Hosting**: Can be deployed to any static file hosting service
2. **CDN**: Consider using a CDN for better performance
3. **HTTPS**: Always use HTTPS in production
4. **Environment Variables**: Update API URLs for production environment
5. **Minification**: Consider minifying CSS and JavaScript files

## Future Enhancements

- Add forgot password functionality
- Implement social login with OAuth
- Add multi-factor authentication support
- Implement progressive web app (PWA) features
- Add internationalization (i18n) support
- Implement dark/light theme toggle
