from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query, Form
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
import asyncio
import logging

from app.core.config import settings
from app.core.jwt import create_access_token, create_refresh_token
from app.core.auth import verify_refresh_token
from app.core.email import send_otp_email, send_password_reset_confirmation_email
from app.db.session import get_db
from app.crud.user import authenticate_user, get_user_by_email, get_user, save_login_token, save_refresh_token, verify_refresh_token as verify_user_refresh_token, update_user_password_by_email
from app.crud.otp import create_otp, verify_otp, use_otp
from app.schemas.token import Token, AccessToken
from app.schemas.user import UserResponse
from app.schemas.password_reset import (
    ForgotPasswordRequest, 
    ForgotPasswordResponse,
    VerifyOTPRequest,
    VerifyOTPResponse,
    ResetPasswordRequest,
    ResetPasswordResponse
)
from app.events.producers.user_events import publish_user_login_event
from app.core.auth import get_current_user, get_current_active_user

router = APIRouter(tags=["authentication"])

@router.post("/login", response_model=Token)
async def login_for_tokens(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """OAuth2 compatible token login, get both access and refresh tokens.

    Note: Use your email address in the 'username' field below.
    """
    user = await authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token (short-lived)
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        subject=user.email, expires_delta=access_token_expires
    )

    # Create refresh token (long-lived)
    refresh_token_expires = timedelta(minutes=settings.REFRESH_TOKEN_EXPIRE_MINUTES)
    refresh_token = create_refresh_token(
        subject=user.email, expires_delta=refresh_token_expires
    )

    # Save the refresh token to the database
    refresh_token_saved = await save_refresh_token(db, user.id, refresh_token)
    if not refresh_token_saved:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save refresh token"
        )

    # Also save access token for backward compatibility (optional)
    await save_login_token(db, user.id, access_token)

    # Publish login event
    await publish_user_login_event(
        user_id=user.id,
        email=user.email,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent")
    )

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }

@router.post("/refresh", response_model=AccessToken)
async def refresh_access_token(
    refresh_token: str = Form(..., description="Refresh token"),
    db: Session = Depends(get_db)
):
    """Exchange refresh token for a new access token"""
    try:
        # Verify the refresh token
        email = verify_refresh_token(refresh_token)
        if not email:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Get user by email
        user = await get_user_by_email(db, email=email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Verify the refresh token matches the stored one
        if not await verify_user_refresh_token(db, user.id, refresh_token):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Create new access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            subject=user.email, expires_delta=access_token_expires
        )

        return {"access_token": access_token, "token_type": "bearer"}

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in refresh_access_token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.get("/debug/users")
async def debug_users(db: Session = Depends(get_db)):
    """Debug endpoint to check users in database"""
    from app.models.user import User
    users = db.query(User).all()
    return {"total_users": len(users), "users": [{"id": u.id, "email": u.email} for u in users]}

@router.get("/test-route")
async def test_route():
    """Simple test route to verify route registration"""
    print("DEBUG: test_route called!")
    return {"message": "Test route is working", "status": "success"}

from app.schemas.user import UserCreate

@router.post("/register", response_model=UserResponse)
async def register_user(
    email: str = Form(...),
    password: str = Form(...),
    confirm_password: str = Form(...),
    first_name: str = Form(...),
    last_name: str = Form(...),
    action: str = Form(None),
    db: Session = Depends(get_db)
):
    """Register new user with email and password"""
    try:
        # Check if user already exists
        existing_user = await get_user_by_email(db, email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        # Create UserCreate object
        user_create = UserCreate(
            email=email,
            password=password,
            confirm_password=confirm_password,
            first_name=first_name,
            last_name=last_name,
            action=action
        )

        # Create new user
        from app.crud.user import create_user
        new_user = await create_user(db, user_create)

        return new_user  # Let FastAPI use from_attributes for UserResponse

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Registration error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not register user"
        )

@router.post("/test-verify")
async def test_verify(token: str = Query(..., description="Token to verify")):
    """Test verify endpoint to debug route issues"""
    print(f"DEBUG: test_verify called with token: {token}")
    return {"message": "Test verify is working", "token_received": token[:20] + "..."}

@router.post("/verify-token", response_model=UserResponse)
async def verify_token(token: str = Query(..., description="JWT token to verify"), db: Session = Depends(get_db)):
    """Verify a token and return the user information"""
    try:
        # Decode JWT token
        from jose import jwt
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        email = payload.get("sub")  # Contains email
        if email is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Look up user by email
        user = await get_user_by_email(db, email=email)
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found",
            )

        # Verify that the token matches the stored login token
        if user.login_token != token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has been invalidated",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return user

    except jwt.JWTError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in verify_token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )

@router.post("/forgot-password", response_model=ForgotPasswordResponse)
async def forgot_password(
    request: ForgotPasswordRequest,
    db: Session = Depends(get_db)
):
    """Request password reset by sending OTP to email"""
    try:
        # Check if user exists
        user = await get_user_by_email(db, email=request.email)
        if user:
            # Create OTP only for valid users
            otp = create_otp(db, user_id=user.id, purpose="password_reset")
            
            # Send OTP email
            email_sent = await send_otp_email(
                email=request.email,
                otp_code=otp.otp_code,
                user_name=f"{user.first_name} {user.last_name}"
            )
            
            if not email_sent:
                logging.error(f"Failed to send OTP email to {request.email}")
        else:
            # Simulate processing time to prevent email enumeration
            await asyncio.sleep(0.5)
        
        # Return consistent response regardless of email existence
        return ForgotPasswordResponse(
            message="If the email exists in our system, you will receive an OTP shortly.",
            email=request.email
        )
        
    except Exception as e:
        logging.error(f"Error in forgot_password: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/verify-otp", response_model=VerifyOTPResponse)
async def verify_otp_endpoint(
    request: VerifyOTPRequest,
    db: Session = Depends(get_db)
):
    """Verify OTP for password reset"""
    try:
        # Check if user exists
        user = await get_user_by_email(db, email=request.email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Verify OTP
        is_valid = verify_otp(db, user_id=user.id, otp_code=request.otp_code, purpose="password_reset")
        
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired OTP"
            )
        
        return VerifyOTPResponse(
            message="OTP verified successfully. You can now reset your password.",
            email=request.email,
            is_valid=True
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in verify_otp: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/reset-password", response_model=ResetPasswordResponse)
async def reset_password(
    request: ResetPasswordRequest,
    db: Session = Depends(get_db)
):
    """Reset password using verified OTP"""
    try:
        # Validate passwords match
        request.validate_passwords_match()
        
        # Check if user exists
        user = await get_user_by_email(db, email=request.email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Verify and use OTP (mark as used)
        otp_used = use_otp(db, user_id=user.id, otp_code=request.otp_code, purpose="password_reset")
        
        if not otp_used:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired OTP"
            )
        
        # Update password
        password_updated = await update_user_password_by_email(db, email=request.email, new_password=request.new_password)
        
        if not password_updated:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update password"
            )
        
        # Send confirmation email
        await send_password_reset_confirmation_email(
            email=request.email,
            user_name=f"{user.first_name} {user.last_name}"
        )
        
        # Publish password changed event
        from app.events.producers.user_events import publish_user_password_changed_event
        await publish_user_password_changed_event(
            user_id=user.id,
            email=user.email
        )
        
        return ResetPasswordResponse(
            message="Password reset successfully. You can now log in with your new password.",
            email=request.email
        )
        
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logging.error(f"Error in reset_password: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """
    Get current user information (protected route example).
    Requires valid access token in Authorization header.
    """
    return current_user

@router.get("/protected")
async def protected_route(current_user: User = Depends(get_current_active_user)):
    """
    Example protected route that requires authentication.
    Requires valid access token in Authorization header.
    """
    from datetime import datetime
    return {
        "message": "This is a protected route",
        "user_id": current_user.id,
        "user_email": current_user.email,
        "access_granted_at": datetime.now().isoformat()
    }