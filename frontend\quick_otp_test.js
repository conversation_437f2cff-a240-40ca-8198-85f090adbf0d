/**
 * Quick OTP Test Script
 * Run this in your browser console to test the enhanced OTP functionality
 * with your Google email address.
 */

// Test configuration
const TEST_CONFIG = {
    AUTH_SERVICE_URL: 'http://localhost:8002',
    YOUR_EMAIL: '<EMAIL>', // Replace with your actual Gmail address
    TEST_PASSWORD: 'newTestPassword123'
};

// Simple message display function
function displayMessage(message, type = 'info') {
    const colors = {
        info: '#007bff',
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107'
    };
    
    console.log(`%c${message}`, `color: ${colors[type]}; font-weight: bold; font-size: 14px;`);
    
    // Also show in page if possible
    if (typeof document !== 'undefined') {
        const existingDiv = document.getElementById('otp-test-messages');
        if (existingDiv) {
            existingDiv.innerHTML += `<div style="color: ${colors[type]}; margin: 5px 0;">${message}</div>`;
        }
    }
}

// Test functions using your enhanced auth.js
async function testOTPFlow() {
    displayMessage('🚀 Starting Enhanced OTP Test Flow', 'info');
    displayMessage('Make sure to replace YOUR_EMAIL in the config above!', 'warning');
    
    try {
        // Step 1: Test service availability
        displayMessage('📡 Checking auth service...', 'info');
        const healthResponse = await fetch(`${TEST_CONFIG.AUTH_SERVICE_URL}/health`);
        
        if (!healthResponse.ok) {
            throw new Error('Auth service not available. Please start it with: docker-compose up auth-service');
        }
        
        displayMessage('✅ Auth service is running!', 'success');
        
        // Step 2: Request OTP
        displayMessage(`📧 Requesting OTP for: ${TEST_CONFIG.YOUR_EMAIL}`, 'info');
        
        const otpResult = await requestPasswordResetOTP(TEST_CONFIG.YOUR_EMAIL);
        
        if (otpResult.success) {
            displayMessage('✅ OTP sent successfully! Check your Gmail inbox.', 'success');
            displayMessage('📱 Look for a professional email with security warnings and a 6-digit code.', 'info');
            displayMessage('⏰ The OTP expires in 5 minutes for security.', 'warning');
            
            // Prompt for OTP
            setTimeout(() => {
                const otpCode = prompt('Enter the 6-digit OTP code you received in your email:');
                if (otpCode && otpCode.length === 6) {
                    testOTPVerification(otpCode);
                } else {
                    displayMessage('❌ Invalid OTP format. Please try again.', 'error');
                }
            }, 2000);
            
        } else {
            displayMessage(`❌ Failed to send OTP: ${otpResult.error}`, 'error');
            if (otpResult.error === 'rate_limited') {
                displayMessage('⏰ Rate limiting is working! Wait 1 minute and try again.', 'warning');
            }
        }
        
    } catch (error) {
        displayMessage(`❌ Error: ${error.message}`, 'error');
    }
}

async function testOTPVerification(otpCode) {
    try {
        displayMessage(`🔐 Verifying OTP: ${otpCode}`, 'info');
        
        const verifyResult = await verifyOTPCode(TEST_CONFIG.YOUR_EMAIL, otpCode);
        
        if (verifyResult.success) {
            displayMessage('✅ OTP verified successfully!', 'success');
            
            // Test password reset
            setTimeout(() => {
                testPasswordReset(otpCode);
            }, 1000);
            
        } else {
            displayMessage('❌ OTP verification failed. Code may be invalid or expired.', 'error');
        }
        
    } catch (error) {
        displayMessage(`❌ Verification error: ${error.message}`, 'error');
    }
}

async function testPasswordReset(otpCode) {
    try {
        displayMessage('🔑 Testing password reset...', 'info');
        
        const resetResult = await resetPasswordWithOTP(
            TEST_CONFIG.YOUR_EMAIL,
            otpCode,
            TEST_CONFIG.TEST_PASSWORD,
            TEST_CONFIG.TEST_PASSWORD
        );
        
        if (resetResult.success) {
            displayMessage('🎉 Password reset completed successfully!', 'success');
            displayMessage('✅ Enhanced OTP notification service is working perfectly!', 'success');
            displayMessage('📊 Test Summary:', 'info');
            displayMessage('  ✅ Cryptographically secure OTP generation', 'success');
            displayMessage('  ✅ Professional email templates', 'success');
            displayMessage('  ✅ Rate limiting protection', 'success');
            displayMessage('  ✅ Secure password reset flow', 'success');
        } else {
            displayMessage('❌ Password reset failed.', 'error');
        }
        
    } catch (error) {
        displayMessage(`❌ Reset error: ${error.message}`, 'error');
    }
}

// Test rate limiting
async function testRateLimiting() {
    displayMessage('⏰ Testing rate limiting (making 5 rapid requests)...', 'info');
    
    for (let i = 1; i <= 5; i++) {
        try {
            displayMessage(`📤 Request ${i}/5...`, 'info');
            
            const result = await requestPasswordResetOTP(TEST_CONFIG.YOUR_EMAIL);
            
            if (result.error === 'rate_limited') {
                displayMessage(`✅ Rate limiting triggered on request ${i}!`, 'success');
                displayMessage('🛡️ Security feature is working correctly.', 'success');
                break;
            } else if (result.success) {
                displayMessage(`✅ Request ${i} succeeded`, 'success');
            } else {
                displayMessage(`❌ Request ${i} failed: ${result.error}`, 'error');
            }
            
            // Small delay between requests
            await new Promise(resolve => setTimeout(resolve, 500));
            
        } catch (error) {
            displayMessage(`❌ Request ${i} error: ${error.message}`, 'error');
        }
    }
}

// Quick setup function
function setupOTPTest() {
    // Create a simple UI for testing
    if (typeof document !== 'undefined') {
        const testDiv = document.createElement('div');
        testDiv.id = 'otp-test-container';
        testDiv.innerHTML = `
            <div style="position: fixed; top: 10px; right: 10px; width: 400px; background: white; border: 2px solid #007bff; border-radius: 8px; padding: 20px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 9999; font-family: Arial, sans-serif;">
                <h3 style="margin: 0 0 15px 0; color: #007bff;">🔐 Enhanced OTP Test</h3>
                <div style="margin: 10px 0;">
                    <input type="email" id="test-email" placeholder="<EMAIL>" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin: 5px 0;">
                </div>
                <button onclick="runOTPTest()" style="background: #007bff; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; margin: 5px;">Test OTP Flow</button>
                <button onclick="runRateLimitTest()" style="background: #ffc107; color: black; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; margin: 5px;">Test Rate Limit</button>
                <button onclick="closeOTPTest()" style="background: #dc3545; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; margin: 5px;">Close</button>
                <div id="otp-test-messages" style="max-height: 200px; overflow-y: auto; margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-size: 12px;"></div>
            </div>
        `;
        
        document.body.appendChild(testDiv);
        
        // Add global functions
        window.runOTPTest = function() {
            const email = document.getElementById('test-email').value;
            if (email) {
                TEST_CONFIG.YOUR_EMAIL = email;
                testOTPFlow();
            } else {
                displayMessage('Please enter your email address first!', 'error');
            }
        };
        
        window.runRateLimitTest = function() {
            const email = document.getElementById('test-email').value;
            if (email) {
                TEST_CONFIG.YOUR_EMAIL = email;
                testRateLimiting();
            } else {
                displayMessage('Please enter your email address first!', 'error');
            }
        };
        
        window.closeOTPTest = function() {
            document.getElementById('otp-test-container').remove();
        };
        
        displayMessage('🎯 OTP Test UI loaded! Enter your Gmail address and click "Test OTP Flow"', 'success');
    }
}

// Console instructions
console.log(`
🔐 Enhanced OTP Test Script Loaded!

Quick Start:
1. setupOTPTest()           - Creates a test UI
2. testOTPFlow()           - Test complete OTP flow
3. testRateLimiting()      - Test security features

Manual Testing:
1. Update TEST_CONFIG.YOUR_EMAIL with your Gmail address
2. Run testOTPFlow() to start testing
3. Check your Gmail for the enhanced OTP email
4. Follow the prompts to complete the test

Enhanced Features:
✅ Cryptographically secure OTP generation
✅ Professional email templates with security warnings  
✅ Rate limiting (max 3 requests per minute)
✅ 5-minute expiration for security
✅ Account lockout protection

Example:
TEST_CONFIG.YOUR_EMAIL = '<EMAIL>';
testOTPFlow();
`);

// Auto-setup if in browser
if (typeof document !== 'undefined') {
    setupOTPTest();
}
