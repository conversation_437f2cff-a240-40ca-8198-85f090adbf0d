# Simple RSA key generation for ZCare microservices
Write-Host "🔑 Generating RSA Keys for ZCare Microservices" -ForegroundColor Green
Write-Host ""

# Create keys directory
if (!(Test-Path "keys")) {
    New-Item -ItemType Directory -Path "keys" | Out-Null
    Write-Host "✅ Created keys directory" -ForegroundColor Green
}

# Generate sample keys (for development only)
$privateKeyContent = @"
-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
wjXGxIbfNQ176i0KnOeHiXOhFuIjHQiHUSOOrjgGObw38MjemmHyFkEjQdL/sp0f
oFnGGWiGi/6rSohyuTjfExHudJRGHpTnPgocnOv/0UrNIDdjFpFI6p1XVb6Ujj45
sxTAi6B8rl0LcWn96cOOCrUCH7YmVBYO5TRjfQjKcnBuNb4ba3xvw1aRMmDSwDdv
moFRoSoieFjZgYzJVVres/4Rt2i2VgEQWgjdxKtNAMBhTtj+3/qjSFnbBQOBHudu
j/mMVFwoNp3lc2lMlTVWbid2nOmrJuQHhPd6p5cZBrDI4U05rtEgVPrVkxKc0A2B
NrLrtm3bAgMBAAECggEBAKTmjaS6tkK8BlPXClTQ2vpz/N6uxDeS35mXpqasqskV
laAidgg/sWqpjXDbXr93otIMLlWsM+X0CqMDgSXKejLS2jx4GDjI1ZplJkO4Y/vT
M8YrUjIwIdwJxoU1HvjEpvt3FX1BVH4DSYO8O70zzBD4iqRaCXEYQ+EUBx+FamFc
nUlMfBg04ctTpbNBAeKdtKlIQVFLxOV6+grokfziDWcmMGlgY9+C7rGJtVqNMN2S
N5chQdpIhuxX/x/pjVZo3jUCrbs8/yS2k3+10uy40hLJ5jxHSPM7p9FKlCiA6of+
WHiuFkFaHwN3zgtXcwMnw/oyAVVqsbHVfPiMtLufBQECgYEA4sBDsQs9PiIBi9rH
9G3UYROhI4J2+odIFMC/WwJulXHfiRMQs+wLoAcC+SfwxgtiRSWigqEb6Cae8Qp9
nBZnMPCaLgHc40gTwHINHlR5XmXuuNNcFx+aHKd5/B2ptfBgjaMBXtEggxrGOcQu
02BCeULrEFRBEcDNfSFLh3xB+F8CgYEA1VQOvqH5nf0UMIWhQi4nkMRg+nDKgHi7
etqzgoXzx2jJWRfOr7dWoNWP+bJpBlpfW2hpZOOtmHx0oEinXAVwmnl66BBomXSq
tmnMQeD2ecTVg3WfZL3mplbOgSudknaAtd9SxGb/2hIYdxtRgotfZ1TR1qriyqI+
adHdvuDcjRECgYEAl2oHpadKiOkvvRaaAqVINGjgJaTbfSuFkQnGqAzpHtPl+k/j
vsjf22JGUMpG9Ag6Iy4+7C5t7yt7GvyzAyPpHHNH4amVwbXr5WcL1Dkw/5B5cQep
kTQj/I1bJMCOBVuUROxXSNDn5c9VolgyKmTqmKVwdG5EbE+aQbSsM5OtOlECgYA9
r+q/SLtw5NeegtGWyucUHAOGnS6+jjMpuAEmlFGxGwrJl3AKMaFqx4RMDVXE8EpQ
nFAwNjGSl0FhWyCqsHBfNtbYMrXewDxMYaq7t9MEQeOb9YqGwz5DFBeQaKXWmL5+
fEiQ/6ot6dM+EyWL0A5MLUdJEfFuLdN77j/ntVzxEQKBgQDTjybfbAO1DuqBfBNR
TnhfpjGtQ0WGeLKAEubxtw1aAoOk+lpuzs/VLQp1yV5cZlxgb0y66LWMqnzrMWAI
fvJiuQs1RdNO8w+sk1sjbFzGzjmSWh+WUOslPf6gMdX5f5G3g5Qo8Aa7djizzeqf
+A5F61gtGAWtnfqrx/+NhALOxw==
-----END PRIVATE KEY-----
"@

$publicKeyContent = @"
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu1SU1L7VLPHCgcI1xsSG
3zUNe+otCpznh4lzoRbiIx0Ih1EjjqI4Bjm8N/DI3pph8hZBI0HS/7KdH6BZxhlo
hov+q0qIcrk43xMR7nSURh6U5z4KHJzr/9FKzSA3YxaRSOqdV1W+lI4+ObMUwIug
fK5dC3Fp/enDjgq1Ah+2JlQWDuU0Y30IynJwbjW+G2t8b8NWkTJg0sA3b5qBUaEq
InhY2YGMyVVa3rP+EbdotlYBEFoI3cSrTQDAYU7Y/t/6o0hZ2wUDgR7nbo/5jFRc
KDad5XNpTJU1Vm4ndpzpqybkB4T3eqeXGQawyOFNOa7RIFT61ZMSnNANgTay67Zt
2wIDAQAB
-----END PUBLIC KEY-----
"@

# Save keys to files
$privateKeyContent | Out-File -FilePath "keys\private.pem" -Encoding UTF8 -NoNewline
$publicKeyContent | Out-File -FilePath "keys\public.pem" -Encoding UTF8 -NoNewline

Write-Host "✅ RSA keys generated successfully" -ForegroundColor Green
Write-Host ""

# Display environment variables
Write-Host "📋 Environment Variables for Render:" -ForegroundColor Blue
Write-Host "====================================" -ForegroundColor Blue
Write-Host ""

Write-Host "RSA_PRIVATE_KEY:"
$privateKeyBase64 = [Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($privateKeyContent))
Write-Host $privateKeyBase64
Write-Host ""

Write-Host "RSA_PUBLIC_KEY:"
$publicKeyBase64 = [Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($publicKeyContent))
Write-Host $publicKeyBase64
Write-Host ""

Write-Host "⚠️  These are SAMPLE keys for development only!" -ForegroundColor Red
Write-Host "⚠️  Generate new keys for production use!" -ForegroundColor Red
Write-Host ""
Write-Host "✅ Keys saved to:" -ForegroundColor Green
Write-Host "   - keys\private.pem"
Write-Host "   - keys\public.pem"
Write-Host ""
Write-Host "🎉 Key generation completed!" -ForegroundColor Green
