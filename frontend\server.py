#!/usr/bin/env python3
"""
Simple HTTP server to serve the frontend files
This avoids CORS issues when accessing the auth service from file:// URLs
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

# Change to the frontend directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

PORT = 3000

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add comprehensive CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        self.send_header('Access-Control-Allow-Credentials', 'true')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200, "OK")
        self.end_headers()

    def do_GET(self):
        # Serve index.html for root path
        if self.path == '/':
            self.path = '/index.html'
        return super().do_GET()

if __name__ == "__main__":
    try:
        handler = CORSHTTPRequestHandler
        with socketserver.TCPServer(("", PORT), handler) as httpd:
            print(f"🚀 Frontend server running at http://localhost:{PORT}")
            print(f"📁 Serving files from: {os.getcwd()}")
            print("🔗 Open http://localhost:3000 in your browser")
            print("⏹️  Press Ctrl+C to stop the server")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n✅ Server stopped.")
    except OSError as e:
        if e.errno == 10048:  # Port already in use
            print(f"❌ Port {PORT} is already in use. Please stop any other servers on this port.")
        else:
            print(f"❌ Error starting server: {e}")
        sys.exit(1)
