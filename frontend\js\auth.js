// Authentication JavaScript for Zionix Frontend

// API Configuration
const API_CONFIG = {
    AUTH_SERVICE_URL: 'http://localhost:8002', // Auth-service runs on port 8002 in Docker
    ENDPOINTS: {
        LOGIN: '/api/v1/login',
        REGISTER: '/api/v1/register',  // Use the form-based auth endpoint
        REFRESH: '/api/v1/refresh'     // Token refresh endpoint
    }
};

// DOM Elements
const loginForm = document.getElementById('loginForm');
const registerForm = document.getElementById('registerForm');
const loginFormElement = document.getElementById('loginFormElement');
const registerFormElement = document.getElementById('registerFormElement');

// Form switching functions
function showLogin() {
    loginForm.style.display = 'block';
    registerForm.style.display = 'none';
    clearForms();
}

function showRegister() {
    loginForm.style.display = 'none';
    registerForm.style.display = 'block';
    clearForms();
}

// Password visibility toggle
function togglePassword(inputId) {
    const passwordInput = document.getElementById(inputId);
    const passwordIcon = document.getElementById(inputId + 'Icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.classList.remove('fa-eye');
        passwordIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        passwordIcon.classList.remove('fa-eye-slash');
        passwordIcon.classList.add('fa-eye');
    }
}

// Clear all forms
function clearForms() {
    loginFormElement.reset();
    registerFormElement.reset();
    clearValidationStates();
}

// Clear validation states
function clearValidationStates() {
    const inputs = document.querySelectorAll('.auth-input');
    inputs.forEach(input => {
        input.classList.remove('is-valid', 'is-invalid');
        const feedback = input.parentNode.querySelector('.invalid-feedback, .valid-feedback');
        if (feedback) {
            feedback.remove();
        }
    });
}

// Show loading state
function showLoading(button) {
    button.classList.add('btn-loading');
    button.disabled = true;
}

// Hide loading state
function hideLoading(button) {
    button.classList.remove('btn-loading');
    button.disabled = false;
}

// Show validation error
function showValidationError(inputId, message) {
    const input = document.getElementById(inputId);
    input.classList.add('is-invalid');
    
    // Remove existing feedback
    const existingFeedback = input.parentNode.querySelector('.invalid-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }
    
    // Add new feedback
    const feedback = document.createElement('div');
    feedback.className = 'invalid-feedback';
    feedback.textContent = message;
    input.parentNode.appendChild(feedback);
}

// Show success message
function showSuccess(message) {
    // You can implement a toast notification or modal here
    alert('Success: ' + message);
}

// Show error message
function showError(message) {
    // You can implement a toast notification or modal here
    alert('Error: ' + message);
}

// Token management functions
function getAccessToken() {
    return localStorage.getItem('access_token');
}

function getRefreshToken() {
    return localStorage.getItem('refresh_token');
}

function setTokens(accessToken, refreshToken) {
    localStorage.setItem('access_token', accessToken);
    localStorage.setItem('refresh_token', refreshToken);
}

function clearTokens() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('token_type');
}

// Check if access token is expired (basic check)
function isTokenExpired(token) {
    if (!token) return true;

    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);
        return payload.exp < currentTime;
    } catch (error) {
        return true;
    }
}

// Refresh access token using refresh token
async function refreshAccessToken() {
    const refreshToken = getRefreshToken();
    if (!refreshToken) {
        throw new Error('No refresh token available');
    }

    try {
        const response = await fetch(`${API_CONFIG.AUTH_SERVICE_URL}${API_CONFIG.ENDPOINTS.REFRESH}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                refresh_token: refreshToken
            })
        });

        if (response.ok) {
            const data = await response.json();
            localStorage.setItem('access_token', data.access_token);
            return data.access_token;
        } else {
            // Refresh token is invalid, clear all tokens
            clearTokens();
            throw new Error('Refresh token expired');
        }
    } catch (error) {
        clearTokens();
        throw error;
    }
}

// Make authenticated API request with automatic token refresh
async function makeAuthenticatedRequest(url, options = {}) {
    let accessToken = getAccessToken();

    // Check if access token is expired and refresh if needed
    if (isTokenExpired(accessToken)) {
        try {
            accessToken = await refreshAccessToken();
        } catch (error) {
            // Redirect to login if refresh fails
            window.location.href = '/login';
            throw error;
        }
    }

    // Add authorization header
    const headers = {
        ...options.headers,
        'Authorization': `Bearer ${accessToken}`
    };

    const response = await fetch(url, {
        ...options,
        headers
    });

    // If we get 401, try to refresh token once more
    if (response.status === 401) {
        try {
            accessToken = await refreshAccessToken();
            headers['Authorization'] = `Bearer ${accessToken}`;

            return await fetch(url, {
                ...options,
                headers
            });
        } catch (error) {
            // Redirect to login if refresh fails
            window.location.href = '/login';
            throw error;
        }
    }

    return response;
}

// Validate email format
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Validate password strength
function isValidPassword(password) {
    // At least 8 characters
    return password.length >= 8;
}

// Login form submission
loginFormElement.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const submitButton = e.target.querySelector('button[type="submit"]');
    const email = document.getElementById('loginEmail').value.trim();
    const password = document.getElementById('loginPassword').value;
    
    // Clear previous validation states
    clearValidationStates();
    
    // Validate inputs
    let isValid = true;
    
    if (!email) {
        showValidationError('loginEmail', 'Email is required');
        isValid = false;
    } else if (!isValidEmail(email)) {
        showValidationError('loginEmail', 'Please enter a valid email address');
        isValid = false;
    }
    
    if (!password) {
        showValidationError('loginPassword', 'Password is required');
        isValid = false;
    }
    
    if (!isValid) return;
    
    // Show loading state
    showLoading(submitButton);
    
    try {
        console.log('Attempting login with URL:', `${API_CONFIG.AUTH_SERVICE_URL}${API_CONFIG.ENDPOINTS.LOGIN}`);
        console.log('Login data:', { email });

        const response = await fetch(`${API_CONFIG.AUTH_SERVICE_URL}${API_CONFIG.ENDPOINTS.LOGIN}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                username: email, // The API expects 'username' but we're sending email
                password: password
            })
        });

        console.log('Login response status:', response.status);
        
        const data = await response.json();
        
        if (response.ok) {
            // Store both access and refresh tokens
            setTokens(data.access_token, data.refresh_token);
            localStorage.setItem('token_type', data.token_type);

            showSuccess('Login successful! Redirecting...');

            // Redirect to dashboard or admin panel
            setTimeout(() => {
                window.location.href = '/dashboard'; // Update this URL as needed
            }, 1500);
        } else {
            showError(data.detail || 'Login failed. Please check your credentials.');
        }
    } catch (error) {
        console.error('Login error:', error);
        showError('Network error. Please try again.');
    } finally {
        hideLoading(submitButton);
    }
});

// Register form submission
registerFormElement.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const submitButton = e.target.querySelector('button[type="submit"]');
    const firstName = document.getElementById('firstName').value.trim();
    const lastName = document.getElementById('lastName').value.trim();
    const email = document.getElementById('registerEmail').value.trim();
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const agreeTerms = document.getElementById('agreeTerms').checked;
    
    // Clear previous validation states
    clearValidationStates();
    
    // Validate inputs
    let isValid = true;
    
    if (!firstName) {
        showValidationError('firstName', 'First name is required');
        isValid = false;
    }
    
    if (!lastName) {
        showValidationError('lastName', 'Last name is required');
        isValid = false;
    }
    
    if (!email) {
        showValidationError('registerEmail', 'Email is required');
        isValid = false;
    } else if (!isValidEmail(email)) {
        showValidationError('registerEmail', 'Please enter a valid email address');
        isValid = false;
    }
    
    if (!password) {
        showValidationError('registerPassword', 'Password is required');
        isValid = false;
    } else if (!isValidPassword(password)) {
        showValidationError('registerPassword', 'Password must be at least 8 characters long');
        isValid = false;
    }
    
    if (!confirmPassword) {
        showValidationError('confirmPassword', 'Please confirm your password');
        isValid = false;
    } else if (password !== confirmPassword) {
        showValidationError('confirmPassword', 'Passwords do not match');
        isValid = false;
    }
    
    if (!agreeTerms) {
        showError('You must agree to the Terms of Use and Privacy Policy');
        isValid = false;
    }
    
    if (!isValid) return;
    
    // Show loading state
    showLoading(submitButton);
    
    try {
        console.log('Attempting registration with URL:', `${API_CONFIG.AUTH_SERVICE_URL}${API_CONFIG.ENDPOINTS.REGISTER}`);
        console.log('Registration data:', { email, firstName, lastName });

        const response = await fetch(`${API_CONFIG.AUTH_SERVICE_URL}${API_CONFIG.ENDPOINTS.REGISTER}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                email: email,
                first_name: firstName,
                last_name: lastName,
                password: password,
                confirm_password: confirmPassword
            })
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        
        const data = await response.json();
        
        if (response.ok) {
            showSuccess('Registration successful! Please log in with your credentials.');
            
            // Switch to login form after successful registration
            setTimeout(() => {
                showLogin();
                // Pre-fill email in login form
                document.getElementById('loginEmail').value = email;
            }, 1500);
        } else {
            if (data.detail) {
                if (typeof data.detail === 'string') {
                    showError(data.detail);
                } else if (Array.isArray(data.detail)) {
                    // Handle validation errors
                    data.detail.forEach(error => {
                        if (error.loc && error.loc.includes('email')) {
                            showValidationError('registerEmail', error.msg);
                        } else {
                            showError(error.msg);
                        }
                    });
                }
            } else {
                showError('Registration failed. Please try again.');
            }
        }
    } catch (error) {
        console.error('Registration error:', error);
        console.error('Error details:', error.message);
        console.error('Error stack:', error.stack);
        showError(`Network error: ${error.message}. Please check if the auth service is running.`);
    } finally {
        hideLoading(submitButton);
    }
});

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    // Show login form by default
    showLogin();
    
    // Check if user is already logged in
    const accessToken = getAccessToken();
    const refreshToken = getRefreshToken();

    if (accessToken && refreshToken) {
        // Check if access token is still valid or can be refreshed
        if (!isTokenExpired(accessToken)) {
            // Optionally redirect to dashboard if already logged in
            // window.location.href = '/dashboard';
        } else {
            // Try to refresh the token
            refreshAccessToken().catch(() => {
                // If refresh fails, clear tokens and stay on login page
                clearTokens();
            });
        }
    }
});
