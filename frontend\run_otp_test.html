<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick OTP Test - Enhanced Notification Service</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .feature-card h3 {
            margin-top: 0;
            color: #fff;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.15);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        .console-output {
            background: rgba(0, 0, 0, 0.7);
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-checking { background: #ffc107; animation: pulse 1s infinite; }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #00ff00;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Enhanced OTP Test Suite</h1>
            <p>Test the enhanced OTP notification service with your Google email</p>
        </div>

        <div class="test-section">
            <h2>🚀 Quick Start</h2>
            <p><span id="serviceStatus" class="status-indicator status-checking"></span>Auth Service Status: <span id="serviceStatusText">Checking...</span></p>
            
            <div style="margin: 20px 0;">
                <input type="email" id="testEmail" placeholder="Enter your Gmail address" 
                       style="padding: 12px; border-radius: 8px; border: none; width: 300px; margin-right: 10px;">
                <button class="button" onclick="startQuickTest()">🧪 Quick Test</button>
            </div>
            
            <div class="instructions">
                <h3>📋 What This Test Does:</h3>
                <ol>
                    <li>✅ Sends a secure OTP to your Gmail</li>
                    <li>📧 You'll receive a professional email with security warnings</li>
                    <li>🔐 Enter the 6-digit code to verify</li>
                    <li>🔑 Complete the password reset flow</li>
                    <li>🛡️ Tests rate limiting and security features</li>
                </ol>
            </div>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>🔒 Security Features</h3>
                <ul>
                    <li>Cryptographically secure OTP</li>
                    <li>5-minute expiration</li>
                    <li>Rate limiting protection</li>
                    <li>Account lockout prevention</li>
                </ul>
            </div>
            <div class="feature-card">
                <h3>📧 Enhanced Emails</h3>
                <ul>
                    <li>Professional design</li>
                    <li>Security warnings</li>
                    <li>Mobile-friendly</li>
                    <li>Clear instructions</li>
                </ul>
            </div>
            <div class="feature-card">
                <h3>🧪 Test Coverage</h3>
                <ul>
                    <li>Complete OTP flow</li>
                    <li>Rate limiting</li>
                    <li>Error handling</li>
                    <li>Security validation</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 Advanced Testing</h2>
            <button class="button" onclick="testRateLimit()">⏰ Test Rate Limiting</button>
            <button class="button" onclick="openConsole()">🖥️ Open Console</button>
            <button class="button" onclick="showInstructions()">📖 Show Instructions</button>
        </div>

        <div id="consoleOutput" class="console-output" style="display: none;">
            <div id="consoleContent">Console output will appear here...</div>
        </div>

        <div class="test-section">
            <h2>📊 Test Results</h2>
            <div id="testResults">
                <p>No tests run yet. Click "Quick Test" to start!</p>
            </div>
        </div>
    </div>

    <!-- Load your enhanced auth.js -->
    <script src="js/auth.js"></script>
    <!-- Load the test script -->
    <script src="quick_otp_test.js"></script>

    <script>
        let testEmail = '';
        let consoleVisible = false;

        // Check service status on load
        window.onload = function() {
            checkServiceStatus();
            
            // Override console.log to show in our UI
            const originalLog = console.log;
            console.log = function(...args) {
                originalLog.apply(console, args);
                if (consoleVisible) {
                    const content = document.getElementById('consoleContent');
                    content.innerHTML += args.join(' ') + '<br>';
                    content.scrollTop = content.scrollHeight;
                }
            };
        };

        async function checkServiceStatus() {
            const statusIndicator = document.getElementById('serviceStatus');
            const statusText = document.getElementById('serviceStatusText');
            
            try {
                const response = await fetch('http://localhost:8002/health');
                if (response.ok) {
                    statusIndicator.className = 'status-indicator status-online';
                    statusText.textContent = 'Online ✅';
                } else {
                    statusIndicator.className = 'status-indicator status-offline';
                    statusText.textContent = 'Error ❌';
                }
            } catch (error) {
                statusIndicator.className = 'status-indicator status-offline';
                statusText.textContent = 'Offline ❌ (Start with: docker-compose up auth-service)';
            }
        }

        function startQuickTest() {
            testEmail = document.getElementById('testEmail').value;
            
            if (!testEmail || !testEmail.includes('@')) {
                alert('Please enter a valid email address!');
                return;
            }
            
            // Update the test config
            TEST_CONFIG.YOUR_EMAIL = testEmail;
            
            // Show console
            openConsole();
            
            // Start the test
            updateTestResults('🚀 Starting enhanced OTP test...', 'info');
            testOTPFlow();
        }

        function testRateLimit() {
            testEmail = document.getElementById('testEmail').value;
            
            if (!testEmail || !testEmail.includes('@')) {
                alert('Please enter a valid email address!');
                return;
            }
            
            TEST_CONFIG.YOUR_EMAIL = testEmail;
            openConsole();
            updateTestResults('⏰ Testing rate limiting...', 'info');
            testRateLimiting();
        }

        function openConsole() {
            const consoleOutput = document.getElementById('consoleOutput');
            consoleOutput.style.display = consoleVisible ? 'none' : 'block';
            consoleVisible = !consoleVisible;
            
            if (consoleVisible) {
                document.getElementById('consoleContent').innerHTML = '🔐 Enhanced OTP Test Console<br>Ready to test...<br><br>';
            }
        }

        function showInstructions() {
            alert(`
🔐 Enhanced OTP Test Instructions:

1. Enter your Gmail address in the input field
2. Click "Quick Test" to start the full OTP flow
3. Check your Gmail inbox for a professional OTP email
4. Enter the 6-digit code when prompted
5. Complete the password reset process

Features being tested:
✅ Cryptographically secure OTP generation
✅ Professional email templates with security warnings
✅ Rate limiting (max 3 requests per minute)
✅ 5-minute expiration for security
✅ Account lockout protection

The test will guide you through each step!
            `);
        }

        function updateTestResults(message, type = 'info') {
            const results = document.getElementById('testResults');
            const colors = {
                info: '#007bff',
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107'
            };
            
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `<div style="color: ${colors[type]}; margin: 5px 0;">[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }

        // Override the displayMessage function to also update our UI
        const originalDisplayMessage = displayMessage;
        displayMessage = function(message, type = 'info') {
            originalDisplayMessage(message, type);
            updateTestResults(message, type);
        };
    </script>
</body>
</html>
