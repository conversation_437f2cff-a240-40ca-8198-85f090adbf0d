from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, DateTime, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.db.session import Base

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(100), unique=True, index=True, nullable=False)
    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)
    hashed_password = Column(String(100), nullable=False)  # Store hashed password
    status = Column(Boolean, default=True)
    action = Column(String(50), nullable=True)
    login_token = Column(Text, nullable=True)  # Store the current access token (deprecated, keeping for compatibility)
    refresh_token = Column(Text, nullable=True)  # Store the current refresh token
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    otps = relationship("OTP", back_populates="user")

    def __repr__(self):
        return f"<User {self.email}>"
