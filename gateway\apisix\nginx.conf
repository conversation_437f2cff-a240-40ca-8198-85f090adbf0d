events {
    worker_connections 1024;
}

http {
    upstream auth_service {
        server auth-service:8000;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=10r/s;

    server {
        listen 9080;
        server_name _;

        # Enable CORS
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;

        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            return 204;
        }

        # Health check endpoint
        location /health {
            return 200 "healthy";
            add_header Content-Type text/plain;
        }

        # Root endpoint
        location = / {
            return 200 '{"message": "ZCare API Gateway - Nginx (Auth Service)", "version": "1.0.0", "services": ["auth-service"], "gateway_type": "nginx", "port": 9080}';
            add_header Content-Type application/json;
        }

        # API routes with rate limiting
        location /api/v1/auth/ {
            limit_req zone=auth_limit burst=30 nodelay;
            
            # Rewrite URL to remove /api/v1/auth prefix
            rewrite ^/api/v1/auth/(.*) /api/v1/$1 break;
            
            proxy_pass http://auth_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # Direct auth routes
        location /auth/ {
            limit_req zone=auth_limit burst=30 nodelay;
            
            # Rewrite URL to remove /auth prefix
            rewrite ^/auth/(.*) /$1 break;
            
            proxy_pass http://auth_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
    }
}
