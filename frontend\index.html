<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zionix - Authentication</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid vh-100">
        <div class="row h-100">
            <div class="col-12 d-flex align-items-center justify-content-center">
                <div class="auth-container">
                    <!-- Login Form -->
                    <div id="loginForm" class="auth-form">
                        <h2 class="auth-title mb-4">Log In</h2>
                        <p class="auth-subtitle mb-4">Enter your email address and password to access admin panel</p>
                        
                        <form id="loginFormElement">
                            <div class="mb-3">
                                <label for="loginEmail" class="form-label">Email</label>
                                <input type="email" class="form-control auth-input" id="loginEmail" placeholder="<EMAIL>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="loginPassword" class="form-label">Password</label>
                                <div class="password-input-container">
                                    <input type="password" class="form-control auth-input" id="loginPassword" placeholder="••••••••••" required>
                                    <button type="button" class="password-toggle" onclick="togglePassword('loginPassword')">
                                        <i class="fas fa-eye" id="loginPasswordIcon"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rememberMe">
                                    <label class="form-check-label" for="rememberMe">
                                        Remember me
                                    </label>
                                </div>
                                <a href="#" class="forgot-password">Forgot Password?</a>
                            </div>
                            
                            <button type="submit" class="btn btn-primary auth-btn w-100 mb-3">Log In</button>
                            
                            <div class="social-login">
                                <button type="button" class="btn btn-outline-light social-btn w-100 mb-2">
                                    <i class="fab fa-google me-2"></i>
                                    Continue with Google
                                </button>
                                <button type="button" class="btn btn-outline-light social-btn w-100 mb-3">
                                    <i class="fab fa-facebook me-2"></i>
                                    Continue with Facebook
                                </button>
                            </div>
                            
                            <p class="text-center auth-switch">
                                Don't have an account? <a href="#" onclick="showRegister()">Create one here</a>
                            </p>
                        </form>
                    </div>
                    
                    <!-- Register Form -->
                    <div id="registerForm" class="auth-form" style="display: none;">
                        <h2 class="auth-title mb-4">Sign Up</h2>
                        
                        <form id="registerFormElement">
                            <div class="mb-3">
                                <label for="firstName" class="form-label">First Name</label>
                                <input type="text" class="form-control auth-input" id="firstName" placeholder="Chris" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="lastName" class="form-label">Last Name</label>
                                <input type="text" class="form-control auth-input" id="lastName" placeholder="Johnson" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="registerEmail" class="form-label">Email</label>
                                <input type="email" class="form-control auth-input" id="registerEmail" placeholder="<EMAIL>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="registerPassword" class="form-label">Password</label>
                                <div class="password-input-container">
                                    <input type="password" class="form-control auth-input" id="registerPassword" placeholder="••••••••" required>
                                    <button type="button" class="password-toggle" onclick="togglePassword('registerPassword')">
                                        <i class="fas fa-eye" id="registerPasswordIcon"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">Confirm Password</label>
                                <div class="password-input-container">
                                    <input type="password" class="form-control auth-input" id="confirmPassword" placeholder="••••••••" required>
                                    <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                                        <i class="fas fa-eye" id="confirmPasswordIcon"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    I agree to the <a href="#" class="terms-link">Terms of Use</a> and <a href="#" class="terms-link">Privacy Policy</a>
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary auth-btn w-100 mb-3">Sign Up</button>
                            
                            <div class="social-login">
                                <button type="button" class="btn btn-outline-light social-btn w-100 mb-2">
                                    <i class="fab fa-google me-2"></i>
                                    Sign up with Google
                                </button>
                                <button type="button" class="btn btn-outline-light social-btn w-100 mb-3">
                                    <i class="fab fa-facebook me-2"></i>
                                    Sign up with Facebook
                                </button>
                            </div>
                            
                            <p class="text-center auth-switch">
                                Already have an account? <a href="#" onclick="showLogin()">Log in here</a>
                            </p>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
</body>
</html>
